<!--components/room-card/room-card.wxml-->
<view class="room-card {{room.status === 'available' ? '' : 'card-disabled'}}" bindtap="onTapCard">
  <!-- 包间图片 -->
  <view class="room-image-container">
    <image 
      class="room-image" 
      src="{{room.images && room.images[0] || '/assets/images/room-default.jpg'}}" 
      mode="aspectFill">
    </image>
    
    <!-- 状态标签 -->
    <view class="room-status {{getStatusClass(room.status)}}">
      {{getStatusText(room.status)}}
    </view>
    
    <!-- 图片数量指示器 -->
    <view class="image-count" wx:if="{{room.images && room.images.length > 1}}">
      <text class="count-icon">📷</text>
      <text class="count-text">{{room.images.length}}</text>
    </view>
  </view>

  <!-- 包间信息 -->
  <view class="room-content">
    <view class="room-header">
      <view class="room-name">{{room.name}}</view>
      <view class="room-price">{{formatPrice(room)}}</view>
    </view>
    
    <view class="room-details">
      <view class="detail-row">
        <view class="detail-item">
          <text class="detail-icon">👥</text>
          <text class="detail-text">{{room.capacity.min}}-{{room.capacity.max}}人</text>
        </view>
        
        <view class="detail-item">
          <text class="detail-icon">📍</text>
          <text class="detail-text">{{room.floor}}楼 {{room.area}}㎡</text>
        </view>
      </view>
      
      <view class="room-features" wx:if="{{room.features && room.features.length > 0}}">
        <view class="feature-tag" wx:for="{{room.features}}" wx:key="index">
          {{item}}
        </view>
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="room-actions {{room.status === 'available' ? '' : 'disabled-section'}}">
      <button 
        class="btn-primary book-btn {{room.status === 'available' ? '' : 'disabled'}}" 
        bindtap="{{room.status === 'available' ? 'onTapBook' : ''}}"
        disabled="{{room.status !== 'available'}}">
        {{room.status === 'available' ? '立即预订' : '暂不可订'}}
      </button>
    </view>
  </view>
</view>