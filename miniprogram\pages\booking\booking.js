// pages/booking/booking.js
const app = getApp()
const { mockDataAPI } = require('../../utils/mockData')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    roomInfo: {
      _id: 'room001',
      name: '雅韵1号包间',
      type: 'private',
      capacity: { min: 8, max: 12 },
      price: { base: 300, peak: 450 },
      status: 'available',
      images: ['/assets/images/environments/environment1.jpg'],
      features: ['独立卫生间', '43寸电视', 'KTV设备'],
      floor: 2,
      area: 25,
      description: '温馨雅致的包间，适合家庭聚餐'
    },
    bookingForm: {
      customerName: '',
      customerPhone: '',
      bookingDate: '',
      timeStart: '',
      timeEnd: '',
      guestCount: 1,
      notes: '',
      deposit: 0
    },
    minDate: '',
    maxDate: '',
    timeSlots: [
      '10:00', '10:30', '11:00', '11:30', '12:00', '12:30',
      '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
      '16:00', '16:30', '17:00', '17:30', '18:00', '18:30',
      '19:00', '19:30', '20:00', '20:30', '21:00', '21:30'
    ],
    selectedTimeSlot: null,
    submitting: false,
    formValid: false,
    formValid: false,
    // 费用信息
    costInfo: {
      roomFee: 0,
      durationFee: 0,
      deposit: 0,
      total: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const roomId = options.roomId
    if (!roomId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 确保数据同步设置，避免渲染闪烁
    this.setData({ roomId })
    this.initDateRange()
    this.loadRoomInfo(roomId)
    this.loadUserInfo()
  },

  /**
   * 初始化日期范围
   */
  initDateRange() {
    const today = new Date()
    const maxDate = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000) // 30天后
    
    this.setData({
      minDate: this.formatDate(today),
      maxDate: this.formatDate(maxDate),
      'bookingForm.bookingDate': this.formatDate(today)
    })
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  /**
   * 加载包间信息
   */
  loadRoomInfo(roomId) {
    const roomInfo = mockDataAPI.getRoomById(roomId)
    
    // 如果找不到房间，返回错误页面
    if (!roomInfo) {
      wx.showToast({
        title: '包间不存在',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }
    
    // 如果房间不可用，也应该阻止进入预订页面
    if (roomInfo.status !== 'available') {
      wx.showToast({
        title: '该包间暂不可预订',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    this.setData({ 
      roomInfo
    })
    this.generateTimeSlots()
    this.updateCostInfo()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        'bookingForm.customerName': userInfo.nickName || '',
        'bookingForm.customerPhone': wx.getStorageSync('customerPhone') || ''
      })
    }
  },

  /**
   * 生成时间段选项
   */
  generateTimeSlots() {
    const slots = []
    for (let hour = 10; hour <= 21; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeStr = `${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`
        slots.push(timeStr)
      }
    }
    this.setData({ timeSlots: slots })
  },

  /**
   * 表单输入处理
   */
  onInputChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    
    this.setData({
      [`bookingForm.${field}`]: value
    }, () => {
      this.validateForm()
    })
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    this.setData({
      'bookingForm.bookingDate': e.detail.value
    }, () => {
      this.validateForm()
    })
  },

  /**
   * 时间选择
   */
  onTimeChange(e) {
    const { field } = e.currentTarget.dataset
    const { value } = e.detail
    const timeStr = this.data.timeSlots[value]
    
    this.setData({
      [`bookingForm.${field}`]: timeStr
    }, () => {
      this.updateCostInfo()
      this.validateForm()
    })
  },

  /**
   * 人数调整
   */
  onGuestCountChange(e) {
    const change = e.currentTarget.dataset.change
    let { guestCount } = this.data.bookingForm
    
    if (change === 'increase') {
      guestCount++
    } else if (change === 'decrease' && guestCount > 1) {
      guestCount--
    }
    
    this.setData({
      'bookingForm.guestCount': guestCount
    }, () => {
      this.validateForm()
    })
  },

  /**
   * 表单验证
   */
  validateForm() {
    const { bookingForm } = this.data
    const isValid = bookingForm.customerName.trim() &&
                   bookingForm.customerPhone.trim() &&
                   bookingForm.bookingDate &&
                   bookingForm.timeStart &&
                   bookingForm.timeEnd &&
                   bookingForm.guestCount > 0
    
    this.setData({ formValid: isValid })
  },

  /**
   * 提交预订
   */
  onSubmitBooking() {
    if (!this.data.formValid || this.data.submitting) {
      return
    }

    // 验证时间逻辑
    if (this.data.bookingForm.timeStart >= this.data.bookingForm.timeEnd) {
      wx.showToast({
        title: '结束时间必须晚于开始时间',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    const orderData = {
      roomId: this.data.roomId,
      roomInfo: this.data.roomInfo,
      booking: {
        date: this.data.bookingForm.bookingDate,
        timeStart: this.data.bookingForm.timeStart,
        timeEnd: this.data.bookingForm.timeEnd,
        guestCount: this.data.bookingForm.guestCount
      },
      customer: {
        name: this.data.bookingForm.customerName.trim(),
        phone: this.data.bookingForm.customerPhone.trim()
      },
      notes: this.data.bookingForm.notes.trim(),
      payment: {
        deposit: this.data.bookingForm.deposit,
        total: this.calculateTotal()
      }
    }

    // 模拟预订成功
    setTimeout(() => {
      // 保存用户信息以便下次使用
      wx.setStorageSync('customerPhone', orderData.customer.phone)
      
      // 模拟生成订单ID
      const orderId = 'order_' + Date.now()
      
      // 使用全局数据API创建订单
      const newOrder = mockDataAPI.createOrder(orderData)
      
      wx.showToast({
        title: '预订成功',
        icon: 'success'
      })
      
      this.setData({ submitting: false })
      
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/orders/orders?orderId=${newOrder._id}`
        })
      }, 1500)
    }, 1000)
  },

  /**
   * 计算包间基础费用
   */
  calculateRoomFee() {
    const { roomInfo, bookingForm } = this.data
    if (!roomInfo || !roomInfo.price) return 0
    
    const basePrice = roomInfo.price.base || 0
    const peakPrice = roomInfo.price.peak || basePrice
    
    // 判断是否为高峰期
    if (bookingForm.timeStart) {
      const hour = parseInt(bookingForm.timeStart.split(':')[0])
      const isPeakTime = hour >= 18 && hour <= 21 // 18:00-21:00为高峰期
      return isPeakTime ? peakPrice : basePrice
    }
    
    return basePrice
  },

  /**
   * 计算用餐时长费用（如果超过3小时额外收费）
   */
  calculateDurationFee() {
    const { bookingForm } = this.data
    if (!bookingForm.timeStart || !bookingForm.timeEnd) return 0
    
    const startHour = parseInt(bookingForm.timeStart.split(':')[0])
    const startMinute = parseInt(bookingForm.timeStart.split(':')[1])
    const endHour = parseInt(bookingForm.timeEnd.split(':')[0])
    const endMinute = parseInt(bookingForm.timeEnd.split(':')[1])
    
    const startTime = startHour * 60 + startMinute
    const endTime = endHour * 60 + endMinute
    const duration = (endTime - startTime) / 60 // 小时
    
    // 超过3小时，每小时加收50元
    if (duration > 3) {
      return Math.ceil(duration - 3) * 50
    }
    
    return 0
  },

  /**
   * 计算总费用
   */
  calculateTotal() {
    const roomFee = this.calculateRoomFee()
    const durationFee = this.calculateDurationFee()
    return roomFee + durationFee
  },

  /**
   * 计算定金
   */
  calculateDeposit(total = null) {
    if (total === null) {
      total = this.calculateTotal()
    }
    // 大厅不收定金
    if (this.data.roomInfo?.type === 'hall') {
      return 0
    }
    // 包间定金为总费用的30%，最低100元
    return Math.max(Math.round(total * 0.3), 100)
  },

  /**
   * 更新费用信息
   */
  updateCostInfo() {
    const roomFee = this.calculateRoomFee()
    const durationFee = this.calculateDurationFee()
    const total = roomFee + durationFee
    const deposit = this.calculateDeposit(total)
    
    this.setData({
      'bookingForm.deposit': deposit,
      'costInfo.roomFee': roomFee,
      'costInfo.durationFee': durationFee,
      'costInfo.deposit': deposit,
      'costInfo.total': total
    })
  },

  /**
   * 获取时间选择器索引
   */
  getTimeIndex(timeStr) {
    return this.data.timeSlots.findIndex(slot => slot === timeStr)
  }
})