/* pages/rooms/rooms.wxss */

/* 筛选栏 */
.filter-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border: 1rpx solid var(--primary-gold);
  border-radius: 20rpx;
  background-color: #ffffff;
  cursor: pointer;
}

.filter-item:active {
  background-color: var(--light-gold);
}

.filter-icon {
  font-size: 28rpx;
}

.filter-text {
  font-size: 24rpx;
  color: var(--primary-gold);
  margin-left: 8rpx;
  font-weight: 500;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  padding: 80rpx 0;
}

.filter-summary {
  flex: 1;
  margin-left: 20rpx;
  display: flex;
  gap: 16rpx;
}

.summary-text {
  font-size: 24rpx;
  color: #ff6b35;
  background-color: #fff2ed;
  padding: 8rpx 12rpx;
  border-radius: 12rpx;
}

.filter-count {
  margin-left: 20rpx;
}

.count-text {
  font-size: 24rpx;
  color: #999;
}

/* 包间列表 */
.rooms-list {
  padding: 20rpx;
}

/* 筛选面板 */
.filter-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.filter-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.filter-reset {
  font-size: 28rpx;
  color: var(--primary-gold);
  font-weight: 500;
}

.filter-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 选项网格 */
.option-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.option-item {
  padding: 16rpx 24rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #333;
  background-color: #ffffff;
  text-align: center;
  min-width: 120rpx;
  cursor: pointer;
}

.option-item.active {
  background-color: var(--primary-gold);
  color: #fff;
  border-color: var(--primary-gold);
}

.option-item:active {
  background-color: #f0f0f0;
}

/* 时间选择器 */
.time-picker-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background-color: #ffffff;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
}

.time-label {
  font-size: 28rpx;
  color: #333;
}

.time-value {
  font-size: 28rpx;
  color: var(--primary-gold);
  padding: 12rpx 20rpx;
  border: 1rpx solid var(--primary-gold);
  border-radius: 8rpx;
  background-color: #fff;
  min-width: 160rpx;
  text-align: center;
  font-weight: 500;
}

/* 筛选面板底部按钮 */
.filter-footer {
  display: flex;
  gap: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.filter-cancel {
  flex: 1;
}

.filter-confirm {
  flex: 2;
}