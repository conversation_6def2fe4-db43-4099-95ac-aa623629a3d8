/**
 * 全局模拟数据管理
 * 统一管理所有测试数据，保证数据一致性
 */

// 包间数据
const ROOMS_DATA = [
  {
    _id: 'room001',
    name: '雅韵1号包间',
    type: 'private',
    capacity: { min: 8, max: 12 },
    price: { base: 300, peak: 450 },
    status: 'available',
    images: ['/assets/images/environments/environment4.jpg'],
    features: ['独立卫生间', '43寸电视', 'KTV设备'],
    floor: 2,
    area: 25,
    description: '温馨雅致的包间，适合家庭聚餐'
  },
  {
    _id: 'room002',
    name: '聚缘2号包间',
    type: 'private',
    capacity: { min: 12, max: 18 },
    price: { base: 500, peak: 680 },
    status: 'available',
    images: ['/assets/images/environments/environment5.jpg'],
    features: ['独立卫生间', '55寸电视', 'KTV设备', '转盘餐桌'],
    floor: 2,
    area: 35,
    description: '宽敞豪华包间，适合商务宴请'
  },
  {
    _id: 'room003',
    name: '和谐3号包间',
    type: 'private',
    capacity: { min: 6, max: 10 },
    price: { base: 280, peak: 380 },
    status: 'occupied',
    images: ['/assets/images/environments/environment5.jpg'],
    features: ['独立卫生间', '43寸电视'],
    floor: 1,
    area: 20,
    description: '精致小包间，适合朋友聚会'
  },
  {
    _id: 'room004',
    name: '华贵4号包间',
    type: 'private',
    capacity: { min: 10, max: 16 },
    price: { base: 600, peak: 800 },
    status: 'maintenance',
    images: ['/assets/images/environments/environment4.jpg'],
    features: ['独立卫生间', '65寸电视', 'KTV设备', '麻将桌'],
    floor: 3,
    area: 40,
    description: '豪华包间，设施齐全'
  },
  {
    _id: 'hall001',
    name: '大厅A区',
    type: 'hall',
    capacity: { min: 2, max: 6 },
    price: { base: 0, peak: 0 },
    status: 'available',
    images: ['/assets/images/environments/environment3.jpg'],
    features: ['开放式', '景观位'],
    floor: 1,
    area: 50,
    description: '开放式大厅，环境优雅'
  },
  {
    _id: 'hall002',
    name: '大厅B区',
    type: 'hall',
    capacity: { min: 4, max: 8 },
    price: { base: 0, peak: 0 },
    status: 'available',
    images: ['/assets/images/environments/environment3.jpg'],
    features: ['开放式', '靠窗位置'],
    floor: 1,
    area: 60,
    description: '靠窗大厅区域，采光良好'
  }
]

// 模拟订单数据
const ORDERS_DATA = [
  {
    _id: 'order_001',
    roomId: 'room001',
    roomInfo: {
      name: '雅韵1号包间',
      type: 'private'
    },
    booking: {
      date: '2025-08-05',
      timeStart: '18:00',
      timeEnd: '21:00',
      guestCount: 10
    },
    customer: {
      name: '张先生',
      phone: '138****8888'
    },
    notes: '不要花生，需要生日蛋糕',
    payment: {
      deposit: 200,
      total: 900,
      method: 'wechat',
      status: 'paid'
    },
    status: 'confirmed', // pending/confirmed/cancelled/completed
    createTime: new Date('2025-08-03 14:30:00'),
    updateTime: new Date('2025-08-03 14:35:00')
  },
  {
    _id: 'order_002',
    roomId: 'room002',
    roomInfo: {
      name: '聚缘2号包间',
      type: 'private'
    },
    booking: {
      date: '2025-08-08',
      timeStart: '12:00',
      timeEnd: '14:00',
      guestCount: 15
    },
    customer: {
      name: '李女士',
      phone: '139****9999'
    },
    notes: '商务聚餐，需要发票',
    payment: {
      deposit: 300,
      total: 1200,
      method: 'wechat',
      status: 'paid'
    },
    status: 'pending',
    createTime: new Date('2025-08-02 16:20:00'),
    updateTime: new Date('2025-08-02 16:20:00')
  },
  {
    _id: 'order_003',
    roomId: 'hall001',
    roomInfo: {
      name: '大厅A区',
      type: 'hall'
    },
    booking: {
      date: '2025-08-04',
      timeStart: '19:00',
      timeEnd: '21:30',
      guestCount: 4
    },
    customer: {
      name: '王先生',
      phone: '137****7777'
    },
    notes: '朋友聚会',
    payment: {
      deposit: 0,
      total: 280,
      method: 'wechat',
      status: 'paid'
    },
    status: 'completed',
    createTime: new Date('2025-08-01 10:15:00'),
    updateTime: new Date('2025-08-04 22:00:00')
  }
]

// 餐厅统计数据
const RESTAURANT_STATS = {
  totalRooms: ROOMS_DATA.filter(room => room.type === 'private').length,
  availableRooms: ROOMS_DATA.filter(room => room.type === 'private' && room.status === 'available').length,
  totalSeats: ROOMS_DATA.filter(room => room.type === 'hall').reduce((sum, hall) => sum + hall.capacity.max, 0),
  availableSeats: ROOMS_DATA.filter(room => room.type === 'hall' && room.status === 'available').reduce((sum, hall) => sum + hall.capacity.max, 0)
}

// 餐厅基本信息
const RESTAURANT_INFO = {
  name: '乐聚轩饭店',
  slogan: '「臻敬的盛宴，您好。您的到来，是我们最大的荣幸」',
  address: '广东省广州市黄埔区荔联街小迳东路3号107国道',
  phone: '020-12345678',
  businessHours: '10:00-22:00',
  description: '欢迎来到本餐厅！我爱中的专属服务，很高兴能为您服务，希望您用餐愉快！',
  managerName: '陈雅坚',
  managerTitle: '专属经理',
  environmentImages: [
    '/assets/images/environments/environment1.jpg',
    '/assets/images/environments/environment2.jpg',
    '/assets/images/environments/environment3.jpg'
  ],
  dishImages: [
    '/assets/images/environments/environment4.jpg',
    '/assets/images/environments/environment5.jpg'
  ]
}

/**
 * 数据操作方法
 */
const mockDataAPI = {
  /**
   * 获取所有包间数据
   */
  getRooms() {
    return [...ROOMS_DATA]
  },

  /**
   * 根据ID获取包间信息
   */
  getRoomById(roomId) {
    return ROOMS_DATA.find(room => room._id === roomId)
  },

  /**
   * 获取餐厅统计数据
   */
  getRestaurantStats() {
    return { ...RESTAURANT_STATS }
  },

  /**
   * 获取所有订单
   */
  getOrders() {
    // 从本地存储获取订单，如果没有则使用默认数据
    const localOrders = wx.getStorageSync('orders') || []
    return localOrders.length > 0 ? localOrders : [...ORDERS_DATA]
  },

  /**
   * 根据ID获取订单
   */
  getOrderById(orderId) {
    const orders = this.getOrders()
    return orders.find(order => order._id === orderId)
  },

  /**
   * 创建新订单
   */
  createOrder(orderData) {
    const orders = this.getOrders()
    const newOrder = {
      ...orderData,
      _id: 'order_' + Date.now(),
      status: 'confirmed',
      createTime: new Date(),
      updateTime: new Date(),
      payment: {
        ...orderData.payment,
        status: 'paid'
      }
    }
    
    orders.push(newOrder)
    wx.setStorageSync('orders', orders)
    return newOrder
  },

  /**
   * 更新订单状态
   */
  updateOrderStatus(orderId, status) {
    const orders = this.getOrders()
    const orderIndex = orders.findIndex(order => order._id === orderId)
    
    if (orderIndex !== -1) {
      orders[orderIndex].status = status
      orders[orderIndex].updateTime = new Date()
      wx.setStorageSync('orders', orders)
      return orders[orderIndex]
    }
    
    return null
  },

  /**
   * 取消订单
   */
  cancelOrder(orderId, reason = '') {
    const orders = this.getOrders()
    const orderIndex = orders.findIndex(order => order._id === orderId)
    
    if (orderIndex !== -1) {
      orders[orderIndex].status = 'cancelled'
      orders[orderIndex].cancelReason = reason
      orders[orderIndex].updateTime = new Date()
      wx.setStorageSync('orders', orders)
      return orders[orderIndex]
    }
    
    return null
  },

  /**
   * 获取订单状态文本
   */
  getOrderStatusText(status) {
    const statusMap = {
      'pending': '待确认',
      'confirmed': '已确认',
      'cancelled': '已取消',
      'completed': '已完成'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取支付状态文本
   */
  getPaymentStatusText(status) {
    const statusMap = {
      'pending': '待支付',
      'paid': '已支付',
      'refunded': '已退款'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取餐厅信息
   */
  getRestaurantInfo() {
    return { ...RESTAURANT_INFO }
  },

  /**
   * 创建邀请函
   */
  createInvitation(orderId) {
    const order = this.getOrderById(orderId)
    if (!order) return null

    const invitationId = 'invitation_' + Date.now()
    const invitation = {
      _id: invitationId,
      orderId: orderId,
      order: order,
      restaurant: RESTAURANT_INFO,
      guests: [], // 确认赴约的客人列表
      createTime: new Date(),
      shareCode: this.generateShareCode()
    }

    // 保存到本地存储
    const invitations = wx.getStorageSync('invitations') || []
    invitations.push(invitation)
    wx.setStorageSync('invitations', invitations)

    return invitation
  },

  /**
   * 生成分享码
   */
  generateShareCode() {
    return Math.random().toString(36).substr(2, 8).toUpperCase()
  },

  /**
   * 根据ID获取邀请函
   */
  getInvitationById(invitationId) {
    const invitations = wx.getStorageSync('invitations') || []
    return invitations.find(inv => inv._id === invitationId)
  },

  /**
   * 根据分享码获取邀请函
   */
  getInvitationByShareCode(shareCode) {
    const invitations = wx.getStorageSync('invitations') || []
    return invitations.find(inv => inv.shareCode === shareCode)
  },

  /**
   * 确认赴约
   */
  confirmAttendance(invitationId, userInfo) {
    const invitations = wx.getStorageSync('invitations') || []
    const invitation = invitations.find(inv => inv._id === invitationId)
    
    if (!invitation) return false

    // 检查是否已经确认过
    const existingGuest = invitation.guests.find(guest => guest.openid === userInfo.openid)
    if (existingGuest) return true

    // 添加客人信息
    invitation.guests.push({
      openid: userInfo.openid,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      confirmTime: new Date()
    })

    wx.setStorageSync('invitations', invitations)
    return true
  },

  /**
   * 获取赴约客人列表
   */
  getInvitationGuests(invitationId) {
    const invitation = this.getInvitationById(invitationId)
    return invitation ? invitation.guests : []
  }
}

module.exports = {
  ROOMS_DATA,
  ORDERS_DATA,
  RESTAURANT_STATS,
  RESTAURANT_INFO,
  mockDataAPI
}