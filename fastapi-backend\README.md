# weapp-EaseBook 易订包间-后台服务端

## 🎈项目简介
易订包间（EaseBook）是一款专为高端餐厅定制的智能包间预订小程序，致力于为商家和用户打造极致的订座体验。
本项目包含三端系统：
- 微信小程序用户端 miniprogram
- 后台管理端 vue3-management
- 服务器端 fastapi-backend

## 🚀项目部署
1. 拉取项目
```bash
git clone https://gitee.com/yoke8/weapp-ease-book.git
```
2. 安装依赖
```bash
pip freeze >> requirements.txt
pip install -r requirements.txt
```
3. 配置数据库
```bash
在.env.dev文件中配置开发环境的数据库和redis
运行sql文件
1.新建数据库easebook(默认，可修改)
2.如果使用的是MySQL数据库，使用命令或数据库连接工具运行sql文件夹下的ruoyi-fastapi.sql；如果使用的是PostgreSQL数据库，使用命令或数据库连接工具运行sql文件夹下的ruoyi-fastapi-pg.sql
```


4. 运行服务
```bash
python3 app.py --env=dev
```

5. 访问
```bash
# 默认账号密码
账号：admin
密码：admin123

# 浏览器访问
地址：http://localhost:80
```

## 🌍线上部署
1. 拉取项目
```bash
git clone https://gitee.com/yoke8/weapp-ease-book.git
```
2. 构建镜像
```shell
docker build -t ye_paint_ocr_image .
```
3. 运行容器 挂载目录
```shell
docker run -d -p 8002:8002 --name ye_paint_ocr --restart=always -v /root/code/ye_paint_ocr:/usr/app ye_paint_ocr_image
```
4. 重启容器
```shell
docker restart ye_paint_ocr
```
5. 进入容器
```shell
docker exec -it ye_paint_ocr bash
```
6. 查看容器日志
```shell
docker logs -f ye_paint_ocr --tail=100
```
7. 容器自启动
```shell
docker update --restart=always ye_paint_ocr
# 取消容器自启动
docker update --restart=no ye_paint_ocr
# 查看容器自启动状态
docker inspect --format='{{.HostConfig.RestartPolicy.Name}}' ye_paint_ocr
```
8. 拉取最新代码
```shell
cd code/ye_paint_ocr
git pull origin dev_beta
cat README.md  # 检查版本
```

## 📈测试
    测试账号：test gw123456


## ⛺接口文档
```bash
# swagger自动化文档
docs地址：http://localhost:9099/dev-api/docs 或 http://localhost:9099/docs
redoc地址：http://localhost:9099/dev-api/redoc 或 http://localhost:9099/redoc
```

## 📊版本更新
**V1.0.20250809.1**
1. 初始化项目
