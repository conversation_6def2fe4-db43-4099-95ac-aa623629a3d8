// pages/orders/orders.js
const { mockDataAPI } = require('../../utils/mockData')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    orders: [],
    filteredOrders: [], // 筛选后的订单
    loading: true,
    currentTab: 'all', // all, pending, confirmed, completed, cancelled
    tabList: [
      { key: 'all', label: '全部' },
      { key: 'pending', label: '待确认' },
      { key: 'confirmed', label: '已确认' },
      { key: 'completed', label: '已完成' }
    ],
    highlightOrderId: null // 高亮显示的订单ID
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 如果有传入订单ID，设置高亮
    if (options.orderId) {
      this.setData({
        highlightOrderId: options.orderId
      })
    }
    
    this.loadOrders()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示时刷新订单数据
    this.loadOrders()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadOrders().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '乐聚轩饭店 - 我的预订',
      path: '/pages/orders/orders'
    }
  },

  /**
   * 加载订单数据
   */
  loadOrders() {
    return new Promise((resolve) => {
      this.setData({ loading: true })
      
      // 模拟加载延迟
      setTimeout(() => {
        const allOrders = mockDataAPI.getOrders()
        console.log('获取到的订单数据:', allOrders)
        
        // 按创建时间倒序排列
        const sortedOrders = allOrders.sort((a, b) => 
          new Date(b.createTime) - new Date(a.createTime)
        )
        
        this.setData({
          orders: sortedOrders,
          loading: false
        })
        
        // 更新筛选后的订单
        this.updateFilteredOrders()
        
        resolve()
      }, 500)
    })
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab
    })
    this.updateFilteredOrders()
  },

  /**
   * 更新筛选后的订单
   */
  updateFilteredOrders() {
    const { orders, currentTab } = this.data
    let filteredOrders
    
    if (currentTab === 'all') {
      filteredOrders = orders
    } else {
      filteredOrders = orders.filter(order => order.status === currentTab)
    }
    
    // 为每个订单添加预处理的显示数据
    const processedOrders = filteredOrders.map(order => ({
      ...order,
      statusClass: this.getStatusClass(order.status),
      statusText: this.getStatusText(order.status),
      paymentStatusText: this.getPaymentStatusText(order.payment.status),
      formattedCreateTime: this.formatDate(order.createTime)
    }))
    
    console.log('处理后的订单数据:', processedOrders)
    
    this.setData({
      filteredOrders: processedOrders
    })
  },

  /**
   * 查看订单详情
   */
  onViewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}`
    })
  },

  /**
   * 取消订单
   */
  onCancelOrder(e) {
    const orderId = e.currentTarget.dataset.id
    
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个预订吗？',
      success: (res) => {
        if (res.confirm) {
          const result = mockDataAPI.cancelOrder(orderId, '用户主动取消')
          
          if (result) {
            wx.showToast({
              title: '取消成功',
              icon: 'success'
            })
            this.loadOrders()
          } else {
            wx.showToast({
              title: '取消失败',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  /**
   * 联系客服
   */
  onContactService() {
    wx.showModal({
      title: '联系客服',
      content: '客服电话：020-12345678\n营业时间：10:00-22:00',
      showCancel: false,
      confirmText: '知道了'
    })
  },

  /**
   * 再次预订
   */
  onBookAgain(e) {
    const orderId = e.currentTarget.dataset.id
    const order = this.data.orders.find(o => o._id === orderId)
    
    if (order) {
      wx.navigateTo({
        url: `/pages/booking/booking?roomId=${order.roomId}`
      })
    }
  },

  /**
   * 打开邀请函
   */
  onOpenInvitation(e) {
    const orderId = e.currentTarget.dataset.id
    
    // 检查是否已存在邀请函
    const invitations = wx.getStorageSync('invitations') || []
    let invitation = invitations.find(inv => inv.orderId === orderId)
    
    if (!invitation) {
      // 如果不存在，创建新的邀请函
      invitation = mockDataAPI.createInvitation(orderId)
      if (!invitation) {
        wx.showToast({
          title: '订单不存在',
          icon: 'error'
        })
        return
      }
    }
    
    wx.navigateTo({
      url: `/pages/invitation/invitation?id=${invitation._id}`
    })
  },

  /**
   * 格式化日期
   */
  formatDate(date) {
    const d = new Date(date)
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  /**
   * 获取订单状态样式
   */
  getStatusClass(status) {
    const classMap = {
      'pending': 'status-pending',
      'confirmed': 'status-confirmed',
      'completed': 'status-completed',
      'cancelled': 'status-cancelled'
    }
    return classMap[status] || ''
  },

  /**
   * 获取订单状态文本
   */
  getStatusText(status) {
    return mockDataAPI.getOrderStatusText(status)
  },

  /**
   * 获取支付状态文本
   */
  getPaymentStatusText(status) {
    return mockDataAPI.getPaymentStatusText(status)
  },

  /**
   * 去预订包间
   */
  onGoBooking() {
    wx.switchTab({
      url: '/pages/rooms/rooms'
    })
  }
})