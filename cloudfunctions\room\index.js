// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const { action } = event

  try {
    switch (action) {
      case 'getRooms':
        return await handleGetRooms(event)
      case 'getRoomDetail':
        return await handleGetRoomDetail(event)
      case 'getStats':
        return await handleGetStats(event)
      case 'updateStatus':
        return await handleUpdateStatus(event)
      default:
        return {
          code: -1,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('包间管理错误:', error)
    return {
      code: -1,
      message: '服务器内部错误',
      error: error.message
    }
  }
}

/**
 * 获取包间列表
 */
async function handleGetRooms(event) {
  const { params = {} } = event
  const { type, capacity, date, timeStart, timeEnd } = params

  try {
    // 构建查询条件
    let whereCondition = {}
    
    // 按类型筛选
    if (type && type !== 'all') {
      whereCondition.type = type
    }
    
    // 按容量筛选
    if (capacity && capacity > 0) {
      if (capacity === 999) {
        // 20人以上
        whereCondition['capacity.min'] = _.gte(20)
      } else {
        // 指定容量范围
        whereCondition['capacity.max'] = _.gte(capacity)
      }
    }

    const result = await db.collection('rooms').where(whereCondition).get()
    let rooms = result.data

    // 如果指定了时间，需要检查时间段冲突
    if (date && timeStart && timeEnd) {
      rooms = await filterAvailableRooms(rooms, date, timeStart, timeEnd)
    }

    return {
      code: 0,
      message: '获取成功',
      data: rooms
    }
  } catch (error) {
    console.error('获取包间列表错误:', error)
    throw error
  }
}

/**
 * 获取包间详情
 */
async function handleGetRoomDetail(event) {
  const { roomId } = event
  
  try {
    const result = await db.collection('rooms').doc(roomId).get()
    
    if (!result.data) {
      return {
        code: -1,
        message: '包间不存在'
      }
    }

    return {
      code: 0,
      message: '获取成功',
      data: result.data
    }
  } catch (error) {
    console.error('获取包间详情错误:', error)
    throw error
  }
}

/**
 * 获取包间统计信息
 */
async function handleGetStats(event) {
  try {
    // 获取所有包间
    const roomsResult = await db.collection('rooms').get()
    const rooms = roomsResult.data

    // 统计包间状态
    const totalRooms = rooms.filter(room => room.type === 'private').length
    const availableRooms = rooms.filter(room => room.type === 'private' && room.status === 'available').length
    
    const totalSeats = rooms.reduce((sum, room) => sum + (room.capacity?.max || 0), 0)
    const availableSeats = rooms.filter(room => room.status === 'available')
      .reduce((sum, room) => sum + (room.capacity?.max || 0), 0)

    return {
      code: 0,
      message: '获取成功',
      data: {
        totalRooms,
        availableRooms,
        totalSeats,
        availableSeats
      }
    }
  } catch (error) {
    console.error('获取统计信息错误:', error)
    throw error
  }
}

/**
 * 更新包间状态
 */
async function handleUpdateStatus(event) {
  const { roomId, status } = event
  
  try {
    const result = await db.collection('rooms').doc(roomId).update({
      data: {
        status: status,
        updateTime: new Date()
      }
    })

    return {
      code: 0,
      message: '更新成功',
      data: result
    }
  } catch (error) {
    console.error('更新包间状态错误:', error)
    throw error
  }
}

/**
 * 过滤可用包间（检查时间冲突）
 */
async function filterAvailableRooms(rooms, date, timeStart, timeEnd) {
  try {
    // 查询指定日期的所有订单
    const ordersResult = await db.collection('orders').where({
      'booking.date': date,
      status: _.in(['pending', 'confirmed'])
    }).get()

    const orders = ordersResult.data

    // 检查每个包间是否有时间冲突
    const availableRooms = rooms.filter(room => {
      const roomOrders = orders.filter(order => order.roomId === room._id)
      
      // 检查是否有时间冲突
      const hasConflict = roomOrders.some(order => {
        const orderStart = order.booking.timeStart
        const orderEnd = order.booking.timeEnd
        
        // 检查时间段是否重叠
        return !(timeEnd <= orderStart || timeStart >= orderEnd)
      })
      
      return !hasConflict && room.status === 'available'
    })

    return availableRooms
  } catch (error) {
    console.error('过滤可用包间错误:', error)
    return rooms.filter(room => room.status === 'available')
  }
}