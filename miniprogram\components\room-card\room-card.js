// components/room-card/room-card.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    room: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 点击卡片
     */
    onTapCard() {
      // 如果房间不可用，不触发跳转
      if (this.properties.room.status !== 'available') {
        wx.showToast({
          title: '该包间暂不可预订',
          icon: 'none'
        })
        return
      }
      
      this.triggerEvent('tap', {
        roomId: this.properties.room._id,
        room: this.properties.room
      })
    },

    /**
     * 点击预订按钮
     */
    onTapBook(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation() // 阻止事件冒泡
      }
      
      if (this.properties.room.status !== 'available') {
        wx.showToast({
          title: '该包间暂不可预订',
          icon: 'none'
        })
        return
      }

      this.triggerEvent('book', {
        roomId: this.properties.room._id,
        room: this.properties.room
      })
    },

    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        'available': '可预订',
        'occupied': '已预订',
        'maintenance': '维护中'
      }
      return statusMap[status] || '未知状态'
    },

    /**
     * 获取状态样式
     */
    getStatusClass(status) {
      return `status-${status}`
    },

    /**
     * 格式化价格
     */
    formatPrice(room) {
      if (room.price && room.price.base) {
        let priceText = `¥${room.price.base}`
        if (room.price.peak && room.price.peak !== room.price.base) {
          priceText += ` - ¥${room.price.peak}`
        }
        return priceText
      }
      return '价格面议'
    },

  }
})