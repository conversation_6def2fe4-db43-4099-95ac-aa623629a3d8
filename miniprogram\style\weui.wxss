/**
 * WeUI 主题样式
 * 基于微信官方设计规范的UI组件库样式
 */

/* 引入WeUI基础变量 */
:root {
  --weui-BG-0: #ededed;
  --weui-BG-1: #f7f7f7;
  --weui-BG-2: #fff;
  --weui-BG-3: #f7f7f7;
  --weui-BG-4: #4c4c4c;
  --weui-BG-5: #fff;
  --weui-FG-0: rgba(0, 0, 0, 0.9);
  --weui-FG-HALF: rgba(0, 0, 0, 0.9);
  --weui-FG-1: rgba(0, 0, 0, 0.55);
  --weui-FG-2: rgba(0, 0, 0, 0.3);
  --weui-FG-3: rgba(0, 0, 0, 0.1);
  --weui-RED: #fa5151;
  --weui-ORANGE: #fa9d3b;
  --weui-YELLOW: #ffc300;
  --weui-GREEN: #91d300;
  --weui-LIGHTGREEN: #95ec69;
  --weui-BRAND: #07c160;
  --weui-BLUE: #10aeff;
  --weui-INDIGO: #1485ee;
  --weui-PURPLE: #6467f0;
  --weui-WHITE: #fff;
  --weui-LINK: #576b95;
  --weui-TEXTGREEN: #06ae56;
  --weui-FG: #000;
  --weui-BG: #fff;
  --weui-TAG-TEXT-ORANGE: #fa9d3b;
  --weui-TAG-BACKGROUND-ORANGE: rgba(250, 157, 59, 0.1);
  --weui-TAG-TEXT-GREEN: #06ae56;
  --weui-TAG-BACKGROUND-GREEN: rgba(6, 174, 86, 0.1);
  --weui-TAG-TEXT-BLUE: #10aeff;
  --weui-TAG-BACKGROUND-BLUE: rgba(16, 174, 255, 0.1);
  --weui-TAG-TEXT-BLACK: rgba(0, 0, 0, 0.5);
  --weui-TAG-BACKGROUND-BLACK: rgba(0, 0, 0, 0.05);
}

/* Cell 样式 */
.weui-cells {
  margin-top: 20rpx;
  background-color: var(--weui-BG-2);
  line-height: 1.41176471;
  font-size: 34rpx;
  overflow: hidden;
  position: relative;
}

.weui-cells:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-cells:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-cell {
  padding: 20rpx 30rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.weui-cell:before {
  content: " ";
  position: absolute;
  left: 30rpx;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-cell:first-child:before {
  display: none;
}

.weui-cell__hd {
  display: inline-block;
  width: 40rpx;
  margin-right: 20rpx;
}

.weui-cell__bd {
  flex: 1;
}

.weui-cell__ft {
  text-align: right;
  color: var(--weui-FG-1);
}

/* Button 样式 */
.weui-btn {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 28rpx;
  padding-right: 28rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  text-align: center;
  text-decoration: none;
  color: var(--weui-BG-2);
  line-height: 2.55555556;
  border-radius: 8rpx;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
}

.weui-btn_primary {
  background-color: var(--weui-BRAND);
}

.weui-btn_primary:not(.weui-btn_disabled):visited {
  color: var(--weui-BG-2);
}

.weui-btn_primary:not(.weui-btn_disabled):active {
  color: rgba(255, 255, 255, 0.6);
  background-color: #06ad56;
}

.weui-btn_default {
  color: var(--weui-FG-0);
  background-color: var(--weui-BG-2);
  border: 1rpx solid var(--weui-FG-3);
}

.weui-btn_default:not(.weui-btn_disabled):active {
  color: rgba(0, 0, 0, 0.6);
  background-color: #e6e6e6;
}

.weui-btn_warn {
  color: var(--weui-BG-2);
  background-color: var(--weui-RED);
}

.weui-btn_warn:not(.weui-btn_disabled):visited {
  color: var(--weui-BG-2);
}

.weui-btn_warn:not(.weui-btn_disabled):active {
  color: rgba(255, 255, 255, 0.6);
  background-color: #c13e3e;
}

.weui-btn_disabled {
  color: rgba(255, 255, 255, 0.6);
  background-color: #06ad56;
}

.weui-btn_loading {
  color: transparent;
}

.weui-btn_mini {
  display: inline-block;
  line-height: 2.3;
  font-size: 26rpx;
  padding: 0 1.32em;
}

/* Form 样式 */
.weui-input {
  width: 100%;
  border: 0;
  outline: 0;
  -webkit-appearance: none;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  height: 2.58823529em;
  line-height: 2.58823529;
}

.weui-textarea {
  display: block;
  border: 0;
  resize: none;
  width: 100%;
  color: inherit;
  font-size: 1em;
  line-height: inherit;
  outline: 0;
}

.weui-label {
  display: block;
  width: 210rpx;
  word-wrap: break-word;
  word-break: break-all;
}

/* Loading 样式 */
.weui-loading {
  margin: 0 10rpx;
  width: 40rpx;
  height: 40rpx;
  display: inline-block;
  vertical-align: middle;
  animation: weuiLoading 1s steps(12, end) infinite;
  background: transparent url(data:image/svg+xml;base64,PHN2ZyBjbGFzcz0iY2lyY3VsYXIiIHZpZXdCb3g9IjI1IDI1IDUwIDUwIj4KICA8Y2lyY2xlIGNsYXNzPSJwYXRoIiBjeD0iNTAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzk5OTk5OSIgc3Ryb2tlLXdpZHRoPSI0IiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiIHN0cm9rZS1kYXNoYXJyYXk9IjE1OSIgc3Ryb2tlLWRhc2hvZmZzZXQ9IjE1OSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CiAgPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiBhdHRyaWJ1dGVUeXBlPSJYTUwiIHR5cGU9InJvdGF0ZSIgZHVyPSIycyIgZnJvbT0iMCA1MCA1MCIgdG89IjM2MCA1MCA1MCIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiLz4KICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJzdHJva2UtZGFzaG9mZnNldCIgZHVyPSIycyIgZnJvbT0iMTU5IiB0bz0iNDAiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIi8+CiAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0ic3Ryb2tlLWRhc2hvZmZzZXQiIGR1cj0iMnMiIGZyb209IjE1OSIgdG89IjQwIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgYmVnaW49IjFzIi8+Cjwvc3ZnPg==) no-repeat;
  background-size: 100%;
}

@keyframes weuiLoading {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

/* Toast 样式 */
.weui-toast {
  position: fixed;
  z-index: 5000;
  width: 7.6em;
  min-height: 7.6em;
  top: 180rpx;
  left: 50%;
  margin-left: -3.8em;
  background: rgba(40, 40, 40, 0.75);
  text-align: center;
  border-radius: 5px;
  color: var(--weui-BG-2);
}

.weui-toast__content {
  margin: 0;
  padding: 50rpx 30rpx 30rpx;
  font-size: 28rpx;
}

.weui-icon_toast {
  margin: 44rpx 0 22rpx;
  display: block;
  width: 110rpx;
  height: 110rpx;
}

/* Panel 样式 */
.weui-panel {
  background-color: var(--weui-BG-2);
  margin-top: 20rpx;
  position: relative;
  overflow: hidden;
}

.weui-panel:first-child {
  margin-top: 0;
}

.weui-panel:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-panel:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 1px;
  border-bottom: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-panel__hd {
  padding: 28rpx 30rpx 20rpx;
  color: var(--weui-FG-1);
  font-size: 26rpx;
  position: relative;
}

.weui-panel__bd {
  border-top: 1rpx solid var(--weui-FG-3);
}

.weui-panel__ft {
  padding: 20rpx 30rpx 28rpx;
  color: var(--weui-FG-1);
  font-size: 26rpx;
  text-align: center;
  position: relative;
}

.weui-panel__ft:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

/* Media Box 样式 */
.weui-media-box {
  padding: 30rpx;
  position: relative;
}

.weui-media-box:before {
  content: " ";
  position: absolute;
  left: 30rpx;
  top: 0;
  right: 0;
  height: 1px;
  border-top: 1rpx solid var(--weui-FG-3);
  color: var(--weui-FG-3);
}

.weui-media-box:first-child:before {
  display: none;
}

.weui-media-box__title {
  font-weight: 400;
  font-size: 34rpx;
  width: auto;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  word-wrap: break-word;
  word-break: break-all;
}

.weui-media-box__desc {
  color: var(--weui-FG-1);
  font-size: 26rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Badge 样式 */
.weui-badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  min-width: 16rpx;
  border-radius: 36rpx;
  background: var(--weui-RED);
  color: var(--weui-BG-2);
  line-height: 1.2;
  text-align: center;
  font-size: 20rpx;
  vertical-align: middle;
}

.weui-badge_dot {
  padding: 8rpx;
  min-width: 0;
}