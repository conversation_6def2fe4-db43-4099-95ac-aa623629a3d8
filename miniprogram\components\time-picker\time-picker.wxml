<!--components/time-picker/time-picker.wxml-->
<view class="time-picker">
  <view class="time-row">
    <!-- 开始时间 -->
    <view class="time-item">
      <view class="time-label">开始时间</view>
      <view 
        class="time-selector {{disabled ? 'disabled' : ''}}" 
        bindtap="showStartTimePicker">
        <text class="time-text {{startTime ? '' : 'placeholder'}}">
          {{startTime || '请选择'}}
        </text>
        <mp-icon icon="arrow-down" size="{{16}}" color="#999"></mp-icon>
      </view>
    </view>

    <!-- 分隔符 */
    <view class="time-divider">至</view>

    <!-- 结束时间 -->
    <view class="time-item">
      <view class="time-label">结束时间</view>
      <view 
        class="time-selector {{disabled || !startTime ? 'disabled' : ''}}" 
        bindtap="showEndTimePicker">
        <text class="time-text {{endTime ? '' : 'placeholder'}}">
          {{endTime || '请选择'}}
        </text>
        <mp-icon icon="arrow-down" size="{{16}}" color="#999"></mp-icon>
      </view>
    </view>
  </view>

  <!-- 开始时间选择器 -->
  <picker
    wx:if="{{showStartPicker}}"
    range="{{timeSlots}}"
    value="{{startIndex >= 0 ? startIndex : 0}}"
    bindchange="onStartTimeChange"
    bindcancel="onCancel">
  </picker>

  <!-- 结束时间选择器 -->
  <picker
    wx:if="{{showEndPicker}}"
    range="{{getAvailableEndTimes()}}"
    value="{{getEndPickerIndex()}}"
    bindchange="onEndTimeChange"
    bindcancel="onCancel">
  </picker>
</view>