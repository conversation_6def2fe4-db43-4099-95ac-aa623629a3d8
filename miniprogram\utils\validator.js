/**
 * 表单验证工具函数
 */

/**
 * 验证手机号码
 * @param {string} phone 手机号码
 * @returns {boolean} 是否有效
 */
function isValidPhone(phone) {
  if (!phone || typeof phone !== 'string') return false
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone.trim())
}

/**
 * 验证邮箱地址
 * @param {string} email 邮箱地址
 * @returns {boolean} 是否有效
 */
function isValidEmail(email) {
  if (!email || typeof email !== 'string') return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email.trim())
}

/**
 * 验证身份证号码
 * @param {string} idCard 身份证号码
 * @returns {boolean} 是否有效
 */
function isValidIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') return false
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard.trim())
}

/**
 * 验证姓名
 * @param {string} name 姓名
 * @returns {boolean} 是否有效
 */
function isValidName(name) {
  if (!name || typeof name !== 'string') return false
  const trimmedName = name.trim()
  return trimmedName.length >= 2 && trimmedName.length <= 20
}

/**
 * 验证密码强度
 * @param {string} password 密码
 * @returns {Object} 验证结果 {valid: boolean, strength: string, message: string}
 */
function validatePassword(password) {
  if (!password || typeof password !== 'string') {
    return { valid: false, strength: 'weak', message: '密码不能为空' }
  }

  if (password.length < 6) {
    return { valid: false, strength: 'weak', message: '密码长度至少6位' }
  }

  if (password.length > 20) {
    return { valid: false, strength: 'weak', message: '密码长度不能超过20位' }
  }

  // 检查密码强度
  let strength = 'weak'
  let score = 0

  // 包含小写字母
  if (/[a-z]/.test(password)) score++
  // 包含大写字母
  if (/[A-Z]/.test(password)) score++
  // 包含数字
  if (/\d/.test(password)) score++
  // 包含特殊字符
  if (/[^a-zA-Z\d]/.test(password)) score++

  if (score >= 3 && password.length >= 8) {
    strength = 'strong'
  } else if (score >= 2 || password.length >= 8) {
    strength = 'medium'
  }

  return {
    valid: true,
    strength,
    message: strength === 'strong' ? '密码强度很好' : 
             strength === 'medium' ? '密码强度一般，建议使用大小写字母、数字和特殊字符' :
             '密码强度较弱，建议使用大小写字母、数字和特殊字符'
  }
}

/**
 * 验证日期格式和有效性
 * @param {string} dateStr 日期字符串 YYYY-MM-DD
 * @returns {boolean} 是否有效
 */
function isValidDate(dateStr) {
  if (!dateStr || typeof dateStr !== 'string') return false
  
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/
  if (!dateRegex.test(dateStr)) return false
  
  const date = new Date(dateStr)
  return !isNaN(date.getTime()) && dateStr === date.toISOString().split('T')[0]
}

/**
 * 验证时间格式
 * @param {string} timeStr 时间字符串 HH:mm
 * @returns {boolean} 是否有效
 */
function isValidTime(timeStr) {
  if (!timeStr || typeof timeStr !== 'string') return false
  
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/
  return timeRegex.test(timeStr)
}

/**
 * 验证数字范围
 * @param {number} value 数值
 * @param {number} min 最小值
 * @param {number} max 最大值
 * @returns {boolean} 是否在范围内
 */
function isInRange(value, min, max) {
  if (typeof value !== 'number' || isNaN(value)) return false
  return value >= min && value <= max
}

/**
 * 验证字符串长度
 * @param {string} str 字符串
 * @param {number} min 最小长度
 * @param {number} max 最大长度
 * @returns {boolean} 是否在长度范围内
 */
function isValidLength(str, min = 0, max = Infinity) {
  if (typeof str !== 'string') return false
  const length = str.trim().length
  return length >= min && length <= max
}

/**
 * 验证包间预订表单
 * @param {Object} formData 表单数据
 * @returns {Object} 验证结果 {valid: boolean, errors: Array}
 */
function validateBookingForm(formData) {
  const errors = []
  
  // 验证客户姓名
  if (!isValidName(formData.customerName)) {
    errors.push('请输入有效的客户姓名（2-20个字符）')
  }
  
  // 验证手机号码
  if (!isValidPhone(formData.customerPhone)) {
    errors.push('请输入有效的手机号码')
  }
  
  // 验证预订日期
  if (!isValidDate(formData.bookingDate)) {
    errors.push('请选择有效的预订日期')
  } else {
    // 检查是否为未来日期
    const bookingDate = new Date(formData.bookingDate)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    
    if (bookingDate < today) {
      errors.push('预订日期不能早于今天')
    }
  }
  
  // 验证时间
  if (!isValidTime(formData.timeStart)) {
    errors.push('请选择有效的开始时间')
  }
  
  if (!isValidTime(formData.timeEnd)) {
    errors.push('请选择有效的结束时间')
  }
  
  // 验证时间逻辑
  if (isValidTime(formData.timeStart) && isValidTime(formData.timeEnd)) {
    if (formData.timeStart >= formData.timeEnd) {
      errors.push('结束时间必须晚于开始时间')
    }
  }
  
  // 验证用餐人数
  if (!isInRange(formData.guestCount, 1, 50)) {
    errors.push('用餐人数必须在1-50人之间')
  }
  
  // 验证备注长度
  if (formData.notes && !isValidLength(formData.notes, 0, 200)) {
    errors.push('备注信息不能超过200个字符')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证用户信息表单
 * @param {Object} userInfo 用户信息
 * @returns {Object} 验证结果
 */
function validateUserInfo(userInfo) {
  const errors = []
  
  if (userInfo.name && !isValidName(userInfo.name)) {
    errors.push('请输入有效的姓名')
  }
  
  if (userInfo.phone && !isValidPhone(userInfo.phone)) {
    errors.push('请输入有效的手机号码')
  }
  
  if (userInfo.email && !isValidEmail(userInfo.email)) {
    errors.push('请输入有效的邮箱地址')
  }
  
  if (userInfo.idCard && !isValidIdCard(userInfo.idCard)) {
    errors.push('请输入有效的身份证号码')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证包间信息（管理员用）
 * @param {Object} roomInfo 包间信息
 * @returns {Object} 验证结果
 */
function validateRoomInfo(roomInfo) {
  const errors = []
  
  if (!isValidLength(roomInfo.name, 2, 20)) {
    errors.push('包间名称长度应为2-20个字符')
  }
  
  if (!['private', 'hall'].includes(roomInfo.type)) {
    errors.push('请选择有效的包间类型')
  }
  
  if (!isInRange(roomInfo.capacity?.min, 1, 100)) {
    errors.push('最小容纳人数应在1-100之间')
  }
  
  if (!isInRange(roomInfo.capacity?.max, 1, 100)) {
    errors.push('最大容纳人数应在1-100之间')
  }
  
  if (roomInfo.capacity?.min >= roomInfo.capacity?.max) {
    errors.push('最大容纳人数应大于最小容纳人数')
  }
  
  if (!isInRange(roomInfo.price?.base, 0, 10000)) {
    errors.push('基础价格应在0-10000之间')
  }
  
  if (roomInfo.price?.peak && !isInRange(roomInfo.price.peak, 0, 10000)) {
    errors.push('高峰价格应在0-10000之间')
  }
  
  if (!isInRange(roomInfo.area, 1, 1000)) {
    errors.push('包间面积应在1-1000平方米之间')
  }
  
  if (!isInRange(roomInfo.floor, 1, 50)) {
    errors.push('楼层应在1-50之间')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 实时验证函数（用于输入时的即时反馈）
 */
const realtimeValidators = {
  /**
   * 手机号码实时验证
   */
  phone: (phone) => {
    if (!phone) return { valid: false, message: '' }
    if (phone.length < 11) return { valid: false, message: '手机号码长度不足' }
    if (phone.length > 11) return { valid: false, message: '手机号码长度超出' }
    if (!isValidPhone(phone)) return { valid: false, message: '手机号码格式不正确' }
    return { valid: true, message: '格式正确' }
  },
  
  /**
   * 姓名实时验证
   */
  name: (name) => {
    if (!name) return { valid: false, message: '' }
    if (name.length < 2) return { valid: false, message: '姓名长度至少2个字符' }
    if (name.length > 20) return { valid: false, message: '姓名长度不能超过20个字符' }
    return { valid: true, message: '格式正确' }
  }
}

module.exports = {
  isValidPhone,
  isValidEmail,
  isValidIdCard,
  isValidName,
  validatePassword,
  isValidDate,
  isValidTime,
  isInRange,
  isValidLength,
  validateBookingForm,
  validateUserInfo,
  validateRoomInfo,
  realtimeValidators
}