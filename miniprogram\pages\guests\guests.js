const { mockDataAPI } = require('../../utils/mockData')

Page({
  data: {
    guests: [],
    invitationId: '',
    loading: true
  },

  onLoad(options) {
    const { invitationId } = options
    this.setData({ invitationId })
    this.loadGuests()
  },

  /**
   * 加载赴约人员列表
   */
  loadGuests() {
    const { invitationId } = this.data
    const guests = mockDataAPI.getInvitationGuests(invitationId)
    
    // 格式化时间
    const formattedGuests = guests.map(guest => ({
      ...guest,
      confirmTime: this.formatTime(guest.confirmTime)
    }))
    
    this.setData({
      guests: formattedGuests,
      loading: false
    })
  },

  /**
   * 格式化时间
   */
  formatTime(date) {
    const d = new Date(date)
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    return `${month}-${day} ${hours}:${minutes}`
  },

  /**
   * 返回邀请函
   */
  goBack() {
    wx.navigateBack()
  }
})