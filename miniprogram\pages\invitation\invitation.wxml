<!-- 邀请函页面 -->
<view class="invitation-container">
  <!-- 背景图 -->
  <view class="background-image">
    <image src="/assets/images/invitation-bg.jpg" mode="aspectFill" class="bg-img"></image>
  </view>
  
  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-text">加载中...</view>
  </view>
  
  <!-- 邀请函内容 -->
  <view wx:else class="invitation-content">
    <!-- 预订成功通知卡片 -->
    <view class="card booking-success-card">
      <view class="card-title">预订成功通知</view>
      <view class="restaurant-name">{{invitation.restaurant.name}}</view>
      <view class="booking-details">
        <view class="detail-row">
          <text class="label">预订套餐：</text>
          <text class="value">{{invitation.order.roomInfo.name}}</text>
        </view>
        <view class="detail-row">
          <text class="label">预订席位：</text>
          <text class="value">{{invitation.order.roomInfo.name}}-V{{invitation.order.roomInfo.capacity.max}}</text>
        </view>
        <view class="detail-row">
          <text class="label">预订日期：</text>
          <text class="value">{{invitation.order.booking.date}}</text>
        </view>
        <view class="detail-row">
          <text class="label">用餐时间：</text>
          <text class="value">{{invitation.order.booking.timeStart}}-{{invitation.order.booking.timeEnd}}</text>
        </view>
        <view class="detail-row">
          <text class="label">用餐人数：</text>
          <text class="value">{{invitation.order.booking.guestCount}}人</text>
        </view>
        <view class="detail-row">
          <text class="label">门店地址：</text>
          <text class="value">{{invitation.restaurant.address}}</text>
        </view>
      </view>
      
      <!-- 已确认人数 -->
      <view class="confirmed-count">
        <text class="count-icon">👥</text>
        <text class="count-text">{{guestCount}}位朋友确认赴约</text>
        <text class="view-list" bindtap="viewGuests">查看名单</text>
      </view>
    </view>

    <!-- 经理联系卡片 -->
    <view class="card manager-card">
      <view class="manager-avatar">
        <image src="/assets/images/manager-avatar.png" class="avatar"></image>
      </view>
      <view class="manager-info">
        <view class="manager-name">{{invitation.restaurant.managerName}}</view>
        <view class="manager-title">{{invitation.restaurant.managerTitle}}</view>
        <view class="manager-desc">{{invitation.restaurant.description}}</view>
      </view>
      <view class="manager-actions">
        <button class="action-btn contact-btn" bindtap="callRestaurant">联系我们</button>
        <button class="action-btn save-btn" bindtap="saveContact">保存名片</button>
      </view>
    </view>

    <!-- 导航卡片 -->
    <view class="card navigation-card" bindtap="navigateToRestaurant">
      <view class="nav-icon">📍</view>
      <view class="nav-info">
        <view class="nav-title">{{invitation.restaurant.name}}</view>
        <view class="nav-address">{{invitation.restaurant.address}}</view>
      </view>
      <view class="nav-action">导航</view>
    </view>

    <!-- 门店环境 -->
    <view class="section-title">门店环境</view>
    <view class="environment-gallery">
      <image 
        wx:for="{{invitation.restaurant.environmentImages}}" 
        wx:key="index"
        src="{{item}}" 
        class="environment-img"
        mode="aspectFill">
      </image>
    </view>

    <!-- 特色菜品 -->
    <view class="section-title">特色菜品</view>
    <view class="dish-gallery">
      <image 
        wx:for="{{invitation.restaurant.dishImages}}" 
        wx:key="index"
        src="{{item}}" 
        class="dish-img"
        mode="aspectFill">
      </image>
    </view>

    <!-- 底部操作按钮 -->
    <view class="bottom-actions">
      <view class="action-buttons">
        <button class="btn-ghost action-btn" bindtap="navigateToStore">导航去门店</button>
        <button 
          wx:if="{{!isOwner && !hasConfirmed}}"
          class="btn-primary action-btn" 
          bindtap="confirmReservation">
          确认赴约
        </button>
        <button 
          wx:if="{{!isOwner && hasConfirmed}}"
          class="btn-success action-btn disabled">
          已确认赴约
        </button>
        <button 
          wx:if="{{isOwner}}"
          class="btn-primary action-btn" 
          open-type="share">
          分享给朋友
        </button>
      </view>
    </view>
  </view>
</view>