<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 轮播图 -->
  <view class="banner-container">
    <swiper 
      class="banner-swiper" 
      indicator-dots="{{true}}" 
      autoplay="{{true}}" 
      interval="4000" 
      duration="500"
      circular="{{true}}"
      bindchange="onBannerChange">
      <swiper-item wx:for="{{bannerImages}}" wx:key="index">
        <image class="banner-image" src="{{item}}" mode="aspectFill"></image>
      </swiper-item>
    </swiper>
    
    <!-- 轮播图遮罩层 -->
    <view class="banner-overlay">
      <view class="banner-content">
        <view class="banner-title">乐聚轩饭店</view>
        <view class="banner-subtitle">传承经典粤菜，品味高端体验</view>
      </view>
    </view>
  </view>

  <!-- 餐厅信息卡片 -->
  <view class="card restaurant-card">
    <view class="card-content">
      <view class="restaurant-header">
        <view class="restaurant-name">{{restaurantInfo.name}}</view>
        <view class="restaurant-rating">
          <text class="rating-score">4.8</text>
          <text class="rating-text">分</text>
        </view>
      </view>
      
      <view class="restaurant-info">
        <view class="info-item">
          <text class="info-icon">📍</text>
          <text class="info-text">{{restaurantInfo.address}}</text>
          <text class="info-action" bindtap="onViewLocation">查看位置</text>
        </view>
        
        <view class="info-item">
          <text class="info-icon">🕐</text>
          <text class="info-text">营业时间：{{restaurantInfo.openTime}}</text>
          <text class="info-action" bindtap="onCallPhone">拨打电话</text>
        </view>
      </view>

      <view class="restaurant-desc">
        {{restaurantInfo.description}}
      </view>

      <view class="restaurant-features">
        <view class="feature-tag" wx:for="{{restaurantInfo.features}}" wx:key="index">
          {{item}}
        </view>
      </view>
    </view>
  </view>

  <!-- 包间状态统计 -->
  <view class="card stats-card">
    <view class="card-content">
      <view class="stats-header">
        <text class="stats-title">实时状态</text>
        <text class="stats-refresh" bindtap="getRoomStats">刷新</text>
      </view>
      
      <view class="stats-grid">
        <view class="stats-item">
          <text class="stats-number theme-color">{{roomStats.availableRooms}}</text>
          <text class="stats-label">可用包间</text>
          <text class="stats-total">共{{roomStats.totalRooms}}间</text>
        </view>
        
        <view class="stats-item">
          <text class="stats-number theme-color">{{roomStats.availableSeats}}</text>
          <text class="stats-label">可用座位</text>
          <text class="stats-total">共{{roomStats.totalSeats}}位</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快速预订按钮 -->
  <view class="quick-booking">
    <button class="btn-primary booking-btn" bindtap="onQuickBooking">
      立即预订包间
    </button>
    <button class="btn-secondary view-btn" bindtap="onViewRooms">
      查看所有包间
    </button>
  </view>

  <!-- 特色菜推荐 -->
  <view class="card dishes-card">
    <view class="card-content">
      <view class="section-header">
        <text class="section-title">特色菜推荐</text>
        <text class="section-more">查看菜单</text>
      </view>
      
      <scroll-view class="dishes-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
        <view class="dishes-list">
          <view class="dish-item" wx:for="{{specialDishes}}" wx:key="id" bindtap="onViewDish" data-id="{{item.id}}">
            <image class="dish-image" src="{{item.image}}" mode="aspectFill"></image>
            <!-- <view class="dish-placeholder">暂无图片</view> -->
            <view class="dish-info">
              <view class="dish-name">{{item.name}}</view>
              <view class="dish-desc">{{item.description}}</view>
              <view class="dish-price">¥{{item.price}}</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>