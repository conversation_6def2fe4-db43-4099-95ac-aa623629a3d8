// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action } = event

  try {
    switch (action) {
      case 'createOrder':
        return await handleCreateOrder(event, wxContext)
      case 'getOrders':
        return await handleGetOrders(event, wxContext)
      case 'getOrderDetail':
        return await handleGetOrderDetail(event, wxContext)
      case 'cancelOrder':
        return await handleCancelOrder(event, wxContext)
      case 'confirmOrder':
        return await handleConfirmOrder(event, wxContext)
      default:
        return {
          code: -1,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('预订管理错误:', error)
    return {
      code: -1,
      message: '服务器内部错误',
      error: error.message
    }
  }
}

/**
 * 创建订单
 */
async function handleCreateOrder(event, wxContext) {
  const { orderData } = event
  const { openid } = wxContext

  try {
    // 验证包间是否可用
    const roomResult = await db.collection('rooms').doc(orderData.roomId).get()
    if (!roomResult.data || roomResult.data.status !== 'available') {
      return {
        code: -1,
        message: '包间暂不可用'
      }
    }

    // 检查时间冲突
    const conflictResult = await db.collection('orders').where({
      roomId: orderData.roomId,
      'booking.date': orderData.booking.date,
      status: _.in(['pending', 'confirmed'])
    }).get()

    const hasConflict = conflictResult.data.some(order => {
      const orderStart = order.booking.timeStart
      const orderEnd = order.booking.timeEnd
      const newStart = orderData.booking.timeStart
      const newEnd = orderData.booking.timeEnd
      
      return !(newEnd <= orderStart || newStart >= orderEnd)
    })

    if (hasConflict) {
      return {
        code: -1,
        message: '该时间段已被预订'
      }
    }

    // 生成订单号
    const orderId = generateOrderId()

    // 创建订单
    const createResult = await db.collection('orders').add({
      data: {
        _id: orderId,
        roomId: orderData.roomId,
        openid: openid,
        roomInfo: orderData.roomInfo,
        customer: orderData.customer,
        booking: orderData.booking,
        payment: {
          deposit: orderData.payment.deposit,
          total: orderData.payment.total,
          method: 'wechat',
          status: 'pending'
        },
        status: 'pending',
        notes: orderData.notes || '',
        createTime: new Date(),
        updateTime: new Date()
      }
    })

    // 更新客户信息
    await updateCustomerInfo(openid, orderData.customer)

    return {
      code: 0,
      message: '预订成功',
      data: {
        orderId: orderId,
        order: createResult
      }
    }
  } catch (error) {
    console.error('创建订单错误:', error)
    throw error
  }
}

/**
 * 获取订单列表
 */
async function handleGetOrders(event, wxContext) {
  const { openid } = wxContext
  const { params = {} } = event

  try {
    let whereCondition = { openid: openid }
    
    // 按状态筛选
    if (params.status) {
      whereCondition.status = params.status
    }

    const result = await db.collection('orders')
      .where(whereCondition)
      .orderBy('createTime', 'desc')
      .get()

    return {
      code: 0,
      message: '获取成功',
      data: result.data
    }
  } catch (error) {
    console.error('获取订单列表错误:', error)
    throw error
  }
}

/**
 * 获取订单详情
 */
async function handleGetOrderDetail(event, wxContext) {
  const { orderId } = event
  const { openid } = wxContext

  try {
    const result = await db.collection('orders').doc(orderId).get()
    
    if (!result.data) {
      return {
        code: -1,
        message: '订单不存在'
      }
    }

    // 验证订单归属
    if (result.data.openid !== openid) {
      return {
        code: -1,
        message: '无权限访问该订单'
      }
    }

    return {
      code: 0,
      message: '获取成功',
      data: result.data
    }
  } catch (error) {
    console.error('获取订单详情错误:', error)
    throw error
  }
}

/**
 * 取消订单
 */
async function handleCancelOrder(event, wxContext) {
  const { orderId, reason } = event
  const { openid } = wxContext

  try {
    // 获取订单信息
    const orderResult = await db.collection('orders').doc(orderId).get()
    if (!orderResult.data) {
      return {
        code: -1,
        message: '订单不存在'
      }
    }

    const order = orderResult.data
    
    // 验证订单归属
    if (order.openid !== openid) {
      return {
        code: -1,
        message: '无权限操作该订单'
      }
    }

    // 检查订单状态
    if (order.status === 'cancelled') {
      return {
        code: -1,
        message: '订单已取消'
      }
    }

    if (order.status === 'completed') {
      return {
        code: -1,
        message: '订单已完成，无法取消'
      }
    }

    // 更新订单状态
    const updateResult = await db.collection('orders').doc(orderId).update({
      data: {
        status: 'cancelled',
        cancelReason: reason || '',
        cancelTime: new Date(),
        updateTime: new Date()
      }
    })

    return {
      code: 0,
      message: '取消成功',
      data: updateResult
    }
  } catch (error) {
    console.error('取消订单错误:', error)
    throw error
  }
}

/**
 * 确认订单（管理员功能）
 */
async function handleConfirmOrder(event, wxContext) {
  const { orderId } = event

  try {
    const result = await db.collection('orders').doc(orderId).update({
      data: {
        status: 'confirmed',
        confirmTime: new Date(),
        updateTime: new Date()
      }
    })

    return {
      code: 0,
      message: '确认成功',
      data: result
    }
  } catch (error) {
    console.error('确认订单错误:', error)
    throw error
  }
}

/**
 * 生成订单号
 */
function generateOrderId() {
  const now = new Date()
  const timestamp = now.getTime().toString()
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `ORDER${timestamp}${random}`
}

/**
 * 更新客户信息
 */
async function updateCustomerInfo(openid, customerData) {
  try {
    const result = await db.collection('customers').where({
      openid: openid
    }).get()

    if (result.data.length > 0) {
      const customer = result.data[0]
      await db.collection('customers').doc(customer._id).update({
        data: {
          name: customerData.name,
          phone: customerData.phone,
          visitCount: _.inc(1),
          lastVisit: new Date(),
          updateTime: new Date()
        }
      })
    }
  } catch (error) {
    console.error('更新客户信息错误:', error)
    // 不抛出错误，避免影响订单创建
  }
}