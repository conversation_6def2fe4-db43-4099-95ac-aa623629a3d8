// pages/index/index.js
const app = getApp()
const { mockDataAPI } = require('../../utils/mockData')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    restaurantInfo: {
      name: '乐聚轩饭店',
      address: '广东省广州市黄埔区荔联街小迳东路3号107国道',
      phone: '020-12345678',
      openTime: '10:00-22:00',
      description: '传承经典粤菜，专注品质服务，提供20个豪华包间和100个雅致卡座',
      features: ['正宗粤菜', '豪华包间', '停车便利', '环境优雅']
    },
    specialDishes: [
      {
        id: 1,
        name: '白切鸡',
        image: '/assets/images/dishes/baiqieji.jpg',
        price: 68,
        description: '选用优质土鸡，配秘制蘸料'
      },
      {
        id: 2,
        name: '卤味拼盘',
        image: '/assets/images/dishes/luwei.jpg',
        price: 38,
        description: '卤汁香浓，营养丰富'
      },
      {
        id: 3,
        name: '烧鹅',
        image: '/assets/images/dishes/shaoe.jpg',
        price: 88,
        description: '皮脆肉嫩，唇齿留香'
      }
    ],
    bannerImages: [
      '/assets/images/environments/environment1.jpg',
      '/assets/images/environments/environment2.jpg',
      '/assets/images/environments/environment3.jpg',
      '/assets/images/environments/environment4.jpg',
      '/assets/images/environments/environment5.jpg'
    ],
    currentBanner: 0,
    roomStats: {
      totalRooms: 20,
      availableRooms: 15,
      totalSeats: 100,
      availableSeats: 76
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadRestaurantData()
    this.startBannerAutoPlay()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getRoomStats()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    this.stopBannerAutoPlay()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.stopBannerAutoPlay()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadRestaurantData()
    this.getRoomStats()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '乐聚轩饭店 - 轻松预订包间',
      path: '/pages/index/index',
      imageUrl: '/assets/images/environments/environment1.jpg'
    }
  },

  /**
   * 加载餐厅数据
   */
  loadRestaurantData() {
    // 这里可以调用云函数获取餐厅信息
    // 暂时使用静态数据
  },

  /**
   * 获取包间统计信息
   */
  getRoomStats() {
    // 使用全局数据API获取统计信息
    const stats = mockDataAPI.getRestaurantStats()
    
    this.setData({
      roomStats: stats
    })
  },

  /**
   * 开始轮播图自动播放
   */
  startBannerAutoPlay() {
    this.bannerTimer = setInterval(() => {
      const { currentBanner, bannerImages } = this.data
      const nextBanner = (currentBanner + 1) % bannerImages.length
      this.setData({
        currentBanner: nextBanner
      })
    }, 3000)
  },

  /**
   * 停止轮播图自动播放
   */
  stopBannerAutoPlay() {
    if (this.bannerTimer) {
      clearInterval(this.bannerTimer)
      this.bannerTimer = null
    }
  },

  /**
   * 轮播图切换
   */
  onBannerChange(e) {
    this.setData({
      currentBanner: e.detail.current
    })
  },

  /**
   * 一键预订
   */
  onQuickBooking() {
    wx.navigateTo({
      url: '/pages/rooms/rooms'
    })
  },

  /**
   * 查看包间
   */
  onViewRooms() {
    wx.switchTab({
      url: '/pages/rooms/rooms'
    })
  },

  /**
   * 拨打电话
   */
  onCallPhone() {
    wx.makePhoneCall({
      phoneNumber: this.data.restaurantInfo.phone,
      fail: (err) => {
        console.error('拨打电话失败：', err)
        wx.showToast({
          title: '拨打电话失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 查看位置
   */
  onViewLocation() {
    wx.openLocation({
      latitude: 23.114202,  // 23.12825
      longitude: 113.552727, // 113.32146
      name: this.data.restaurantInfo.name,
      address: this.data.restaurantInfo.address,
      scale: 18
    })
  },

  /**
   * 查看特色菜详情
   */
  onViewDish(e) {
    const dishId = e.currentTarget.dataset.id
    // 可以跳转到菜品详情页
    wx.showToast({
      title: '功能开发中...',
      icon: 'none'
    })
  }
})