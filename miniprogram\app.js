//app.js
App({
  onLaunch() {
    console.log('小程序启动')

    // 获取用户信息
    this.globalData = {
      userInfo: null,
      hasLogin: false,
      openid: null,
      systemInfo: null
    }

    // 获取系统信息
    wx.getSystemInfo({
      success: (res) => {
        this.globalData.systemInfo = res
      }
    })

    // 检查登录状态
    this.checkLoginStatus()
  },

  onShow() {
    // 小程序显示时的处理
  },

  onHide() {
    // 小程序隐藏时的处理
  },

  onError(error) {
    // 小程序错误处理
    console.error('小程序发生错误：', error)
  },

  /**
   * 检查用户登录状态
   */
  checkLoginStatus() {
    const openid = wx.getStorageSync('openid')
    if (openid) {
      this.globalData.openid = openid
      this.globalData.hasLogin = true
    }
  },

  /**
   * 用户登录 (简化版本，无需云函数)
   */
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 模拟生成openid
            const mockOpenid = 'mock_openid_' + Math.random().toString(36).substr(2, 9)
            this.globalData.openid = mockOpenid
            this.globalData.hasLogin = true
            wx.setStorageSync('openid', mockOpenid)
            resolve({ openid: mockOpenid })
          } else {
            reject('获取登录code失败')
          }
        },
        fail: reject
      })
    })
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (this.globalData.userInfo) {
        resolve(this.globalData.userInfo)
        return
      }

      wx.getUserProfile({
        desc: '用于完善会员资料',
        success: (res) => {
          this.globalData.userInfo = res.userInfo
          resolve(res.userInfo)
        },
        fail: reject
      })
    })
  },

  globalData: {
    userInfo: null,
    hasLogin: false,
    openid: null,
    systemInfo: null
  }
})