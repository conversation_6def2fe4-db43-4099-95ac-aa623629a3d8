// components/time-picker/time-picker.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    startTime: {
      type: String,
      value: ''
    },
    endTime: {
      type: String,
      value: ''
    },
    minTime: {
      type: String,
      value: '10:00'
    },
    maxTime: {
      type: String,
      value: '22:00'
    },
    step: {
      type: Number,
      value: 30 // 时间间隔，单位分钟
    },
    disabled: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    timeSlots: [],
    startIndex: -1,
    endIndex: -1,
    showStartPicker: false,
    showEndPicker: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.generateTimeSlots()
      this.updateIndexes()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 生成时间段选项
     */
    generateTimeSlots() {
      const slots = []
      const [minHour, minMinute] = this.properties.minTime.split(':').map(Number)
      const [maxHour, maxMinute] = this.properties.maxTime.split(':').map(Number)
      const step = this.properties.step

      let currentHour = minHour
      let currentMinute = minMinute

      while (currentHour < maxHour || (currentHour === maxHour && currentMinute <= maxMinute)) {
        const timeStr = `${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}`
        slots.push(timeStr)

        currentMinute += step
        if (currentMinute >= 60) {
          currentMinute = 0
          currentHour++
        }
      }

      this.setData({ timeSlots: slots })
    },

    /**
     * 更新选中的索引
     */
    updateIndexes() {
      const { timeSlots } = this.data
      const { startTime, endTime } = this.properties

      const startIndex = timeSlots.findIndex(time => time === startTime)
      const endIndex = timeSlots.findIndex(time => time === endTime)

      this.setData({
        startIndex: startIndex >= 0 ? startIndex : -1,
        endIndex: endIndex >= 0 ? endIndex : -1
      })
    },

    /**
     * 显示开始时间选择器
     */
    showStartTimePicker() {
      if (this.properties.disabled) return
      this.setData({ showStartPicker: true })
    },

    /**
     * 显示结束时间选择器
     */
    showEndTimePicker() {
      if (this.properties.disabled) return
      this.setData({ showEndPicker: true })
    },

    /**
     * 开始时间选择
     */
    onStartTimeChange(e) {
      const index = e.detail.value
      const selectedTime = this.data.timeSlots[index]
      
      this.setData({
        startIndex: index,
        showStartPicker: false
      })

      // 如果结束时间早于或等于开始时间，清空结束时间
      if (this.data.endIndex >= 0 && this.data.endIndex <= index) {
        this.setData({ endIndex: -1 })
        this.triggerEvent('change', {
          startTime: selectedTime,
          endTime: ''
        })
      } else {
        this.triggerEvent('change', {
          startTime: selectedTime,
          endTime: this.properties.endTime
        })
      }
    },

    /**
     * 结束时间选择
     */
    onEndTimeChange(e) {
      const index = e.detail.value
      const selectedTime = this.data.timeSlots[index]
      
      // 确保结束时间晚于开始时间
      if (this.data.startIndex >= 0 && index <= this.data.startIndex) {
        wx.showToast({
          title: '结束时间不能早于开始时间',
          icon: 'none'
        })
        return
      }

      this.setData({
        endIndex: index,
        showEndPicker: false
      })

      this.triggerEvent('change', {
        startTime: this.properties.startTime,
        endTime: selectedTime
      })
    },

    /**
     * 取消选择
     */
    onCancel() {
      this.setData({
        showStartPicker: false,
        showEndPicker: false
      })
    },

    /**
     * 获取可选的结束时间列表（过滤掉早于开始时间的选项）
     */
    getAvailableEndTimes() {
      const { timeSlots, startIndex } = this.data
      if (startIndex < 0) return timeSlots
      
      return timeSlots.slice(startIndex + 1)
    },

    /**
     * 获取结束时间选择器的索引
     */
    getEndPickerIndex() {
      const { endIndex, startIndex } = this.data
      if (endIndex < 0 || startIndex < 0) return 0
      
      return Math.max(0, endIndex - startIndex - 1)
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'startTime, endTime': function(startTime, endTime) {
      this.updateIndexes()
    }
  }
})