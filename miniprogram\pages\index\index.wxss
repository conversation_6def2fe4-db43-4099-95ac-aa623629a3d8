/* pages/index/index.wxss */

/* 页面背景 */
.page-container {
  background: linear-gradient(135deg, #FAFAFA 0%, #F5F5F5 100%);
  position: relative;
}

.page-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('/assets/images/environments/environment3.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.03;
  z-index: -1;
  pointer-events: none;
}

/* 轮播图样式 */
.banner-container {
  position: relative;
  height: 500rpx;
  overflow: hidden;
  /* border-radius: 0 0 32rpx 32rpx; */
}

.banner-swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 轮播图遮罩层 */
.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 40%, rgba(42, 45, 52, 0.3) 70%, rgba(42, 45, 52, 0.6) 100%);
  display: flex;
  align-items: flex-end;
  z-index: 10;
  pointer-events: none;
}

.banner-content {
  width: 100%;
  padding: 48rpx 40rpx;
  color: white;
}

.banner-title {
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 2rpx;
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  font-weight: 400;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 餐厅信息卡片 */
.restaurant-card {
  margin-top: 20rpx;
}

.restaurant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.restaurant-name {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 2rpx;
}

.restaurant-rating {
  display: flex;
  align-items: baseline;
}

.rating-score {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--primary-gold);
}

.rating-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 4rpx;
}

.restaurant-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
}

.info-text {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  margin-left: 16rpx;
}

.info-action {
  font-size: 24rpx;
  color: #ff6b35;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b35;
  border-radius: 8rpx;
}

.restaurant-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.restaurant-features {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.feature-tag {
  font-size: 24rpx;
  color: #ff6b35;
  background-color: #fff2ed;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
}

/* 统计卡片 */
.stats-card {
  margin-top: 20rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.stats-refresh {
  font-size: 24rpx;
  color: #ff6b35;
}

.stats-grid {
  display: flex;
  gap: 40rpx;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stats-number {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: var(--primary-gold);
  letter-spacing: 1rpx;
  margin-bottom: 8rpx;
}

.stats-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.stats-total {
  display: block;
  font-size: 24rpx;
  color: #999;
}

/* 快速预订 */
.quick-booking {
  margin: 32rpx;
  display: flex;
  gap: 24rpx;
}

.booking-btn {
  flex: 1;
}

.view-btn {
  flex: 1;
}

/* 特色菜推荐 */
.dishes-card {
  margin-top: 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 24rpx;
  color: #ff6b35;
}

.dishes-scroll {
  width: 100%;
  white-space: nowrap;
}

.dishes-list {
  display: inline-flex;
  gap: 20rpx;
  padding-bottom: 10rpx;
}

.dish-item {
  display: inline-block;
  width: 240rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.dish-image {
  width: 100%;
  height: 180rpx;
}

.dish-placeholder {
  width: 100%;
  height: 180rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 24rpx;
  border-radius: 12rpx;
}

.dish-info {
  padding: 16rpx;
}

.dish-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.dish-desc {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.dish-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}