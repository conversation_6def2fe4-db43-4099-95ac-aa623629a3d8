/**app.wxss - 易订包间高端设计风格**/
@import "style/weui.wxss";

/* ==================== 设计规范变量 ==================== */
page {
  /* 主色调：鍏金色 + 深空灰 */
  --primary-gold: #D4AF37;
  --primary-dark: #2A2D34;
  --secondary-gold: #E8C547;
  --light-gold: #F4E9A1;
  
  /* 辅助色 */
  --text-primary: #2A2D34;
  --text-secondary: #666666;
  --text-light: #999999;
  --bg-primary: #FFFFFF;
  --bg-secondary: #FAFAFA;
  --border-color: rgba(212, 175, 55, 0.2);
  
  /* 字体设置 */
  font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-secondary);
  font-size: 28rpx;
  line-height: 1.7;
  color: var(--text-primary);
}

/* 主题色彩 */
.theme-color {
  color: var(--primary-gold) !important;
}

.theme-bg {
  background-color: var(--primary-gold) !important;
}

/* 通用容器 */
.container {
  padding: 20rpx;
  margin: 0 auto;
  background-color: #fff;
}

.page-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 高端卡片样式 */
.card {
  background: linear-gradient(135deg, #FFFFFF 0%, #FEFEFE 100%);
  border-radius: 24rpx;
  box-shadow: 0 12rpx 48rpx rgba(42, 45, 52, 0.08), 0 4rpx 16rpx rgba(212, 175, 55, 0.04);
  margin: 24rpx 32rpx;
  overflow: hidden;
  border: 1rpx solid var(--border-color);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
}

.card-content {
  padding: 40rpx;
}

/* 高端按钮样式 */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-gold) 0%, var(--secondary-gold) 100%);
  color: #FFFFFF;
  border: none;
  border-radius: 48rpx;
  padding: 28rpx 56rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  border: none;
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-gold);
  border: 2rpx solid var(--primary-gold);
  border-radius: 48rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary::after {
  border: none;
}

.btn-ghost {
  background-color: transparent;
  color: #999;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 22rpx 46rpx;
  font-size: 28rpx;
}

/* 表单样式 */
.form-item {
  margin-bottom: 40rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #ff6b35;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 24rpx;
  font-size: 32rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
  box-sizing: border-box;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 文字样式 */
.text-primary {
  color: #ff6b35;
}

.text-secondary {
  color: #666;
}

.text-muted {
  color: #999;
}

.text-success {
  color: #52c41a;
}

.text-warning {
  color: #faad14;
}

.text-danger {
  color: #f5222d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

.text-large {
  font-size: 36rpx;
}

.text-small {
  font-size: 24rpx;
}

.text-mini {
  font-size: 20rpx;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }

/* 状态样式 */
.status-available {
  color: #52c41a;
  background-color: #f6ffed;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.status-occupied {
  color: #f5222d;
  background-color: #fff2f0;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

.status-maintenance {
  color: #faad14;
  background-color: #fffbe6;
  padding: 8rpx 16rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
}

/* 遮罩层 */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 80rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

/* 图片样式 */
.image-cover {
  object-fit: cover;
}

.image-contain {
  object-fit: contain;
}

/* 动画 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 安全区域适配 */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}