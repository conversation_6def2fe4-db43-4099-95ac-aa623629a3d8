/**
 * 日期处理工具函数
 */

/**
 * 格式化日期
 * @param {Date|string|number} date 日期对象、日期字符串或时间戳
 * @param {string} format 格式化模板，支持 YYYY-MM-DD HH:mm:ss
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return ''
  
  const d = new Date(date)
  if (isNaN(d.getTime())) return ''

  const year = d.getFullYear()
  const month = String(d.getMonth() + 1).padStart(2, '0')
  const day = String(d.getDate()).padStart(2, '0')
  const hour = String(d.getHours()).padStart(2, '0')
  const minute = String(d.getMinutes()).padStart(2, '0')
  const second = String(d.getSeconds()).padStart(2, '0')

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hour)
    .replace('mm', minute)
    .replace('ss', second)
}

/**
 * 获取今天的日期字符串
 * @returns {string} YYYY-MM-DD格式的今天日期
 */
function getToday() {
  return formatDate(new Date(), 'YYYY-MM-DD')
}

/**
 * 获取明天的日期字符串
 * @returns {string} YYYY-MM-DD格式的明天日期
 */
function getTomorrow() {
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return formatDate(tomorrow, 'YYYY-MM-DD')
}

/**
 * 获取指定天数后的日期
 * @param {number} days 天数
 * @param {Date} baseDate 基准日期，默认为今天
 * @returns {string} YYYY-MM-DD格式的日期
 */
function getDateAfter(days, baseDate = new Date()) {
  const date = new Date(baseDate)
  date.setDate(date.getDate() + days)
  return formatDate(date, 'YYYY-MM-DD')
}

/**
 * 获取指定天数前的日期
 * @param {number} days 天数
 * @param {Date} baseDate 基准日期，默认为今天
 * @returns {string} YYYY-MM-DD格式的日期
 */
function getDateBefore(days, baseDate = new Date()) {
  const date = new Date(baseDate)
  date.setDate(date.getDate() - days)
  return formatDate(date, 'YYYY-MM-DD')
}

/**
 * 计算两个日期之间的天数差
 * @param {Date|string} startDate 开始日期
 * @param {Date|string} endDate 结束日期
 * @returns {number} 天数差
 */
function getDaysDiff(startDate, endDate) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  
  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return 0
  }
  
  const diffTime = end.getTime() - start.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

/**
 * 判断是否为今天
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为今天
 */
function isToday(date) {
  const target = new Date(date)
  const today = new Date()
  
  return target.getFullYear() === today.getFullYear() &&
         target.getMonth() === today.getMonth() &&
         target.getDate() === today.getDate()
}

/**
 * 判断是否为明天
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为明天
 */
function isTomorrow(date) {
  const target = new Date(date)
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  return target.getFullYear() === tomorrow.getFullYear() &&
         target.getMonth() === tomorrow.getMonth() &&
         target.getDate() === tomorrow.getDate()
}

/**
 * 判断是否为工作日
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为工作日（周一到周五）
 */
function isWeekday(date) {
  const day = new Date(date).getDay()
  return day >= 1 && day <= 5
}

/**
 * 判断是否为周末
 * @param {Date|string} date 要判断的日期
 * @returns {boolean} 是否为周末（周六和周日）
 */
function isWeekend(date) {
  const day = new Date(date).getDay()
  return day === 0 || day === 6
}

/**
 * 获取星期几的中文名称
 * @param {Date|string} date 日期
 * @returns {string} 星期几
 */
function getWeekName(date) {
  const weekNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const day = new Date(date).getDay()
  return weekNames[day]
}

/**
 * 获取月份的中文名称
 * @param {Date|string} date 日期
 * @returns {string} 月份
 */
function getMonthName(date) {
  const month = new Date(date).getMonth() + 1
  return `${month}月`
}

/**
 * 生成时间段选项
 * @param {string} startTime 开始时间 HH:mm
 * @param {string} endTime 结束时间 HH:mm
 * @param {number} step 间隔分钟数
 * @returns {Array} 时间段数组
 */
function generateTimeSlots(startTime = '10:00', endTime = '22:00', step = 30) {
  const slots = []
  const [startHour, startMinute] = startTime.split(':').map(Number)
  const [endHour, endMinute] = endTime.split(':').map(Number)

  let currentHour = startHour
  let currentMinute = startMinute

  while (currentHour < endHour || (currentHour === endHour && currentMinute <= endMinute)) {
    const timeStr = `${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}`
    slots.push(timeStr)

    currentMinute += step
    if (currentMinute >= 60) {
      currentMinute = 0
      currentHour++
    }
  }

  return slots
}

/**
 * 时间比较
 * @param {string} time1 时间1 HH:mm
 * @param {string} time2 时间2 HH:mm
 * @returns {number} -1: time1 < time2, 0: time1 = time2, 1: time1 > time2
 */
function compareTime(time1, time2) {
  const [h1, m1] = time1.split(':').map(Number)
  const [h2, m2] = time2.split(':').map(Number)
  
  const minutes1 = h1 * 60 + m1
  const minutes2 = h2 * 60 + m2
  
  if (minutes1 < minutes2) return -1
  if (minutes1 > minutes2) return 1
  return 0
}

/**
 * 计算时间差（分钟）
 * @param {string} startTime 开始时间 HH:mm
 * @param {string} endTime 结束时间 HH:mm
 * @returns {number} 时间差（分钟）
 */
function getTimeDiffMinutes(startTime, endTime) {
  const [startHour, startMinute] = startTime.split(':').map(Number)
  const [endHour, endMinute] = endTime.split(':').map(Number)
  
  const startMinutes = startHour * 60 + startMinute
  const endMinutes = endHour * 60 + endMinute
  
  return endMinutes - startMinutes
}

/**
 * 获取相对时间描述
 * @param {Date|string} date 日期
 * @returns {string} 相对时间描述
 */
function getRelativeTime(date) {
  const target = new Date(date)
  const now = new Date()
  const diffMs = target.getTime() - now.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (isToday(date)) {
    return '今天'
  } else if (isTomorrow(date)) {
    return '明天'
  } else if (diffDays > 0 && diffDays <= 7) {
    return getWeekName(date)
  } else if (diffDays > 7) {
    return formatDate(date, 'MM-DD')
  } else {
    return formatDate(date, 'YYYY-MM-DD')
  }
}

/**
 * 判断时间是否在营业时间内
 * @param {string} time 时间 HH:mm
 * @param {string} openTime 营业开始时间 HH:mm
 * @param {string} closeTime 营业结束时间 HH:mm
 * @returns {boolean} 是否在营业时间内
 */
function isBusinessTime(time, openTime = '10:00', closeTime = '22:00') {
  return compareTime(time, openTime) >= 0 && compareTime(time, closeTime) <= 0
}

module.exports = {
  formatDate,
  getToday,
  getTomorrow,
  getDateAfter,
  getDateBefore,
  getDaysDiff,
  isToday,
  isTomorrow,
  isWeekday,
  isWeekend,
  getWeekName,
  getMonthName,
  generateTimeSlots,
  compareTime,
  getTimeDiffMinutes,
  getRelativeTime,
  isBusinessTime
}