# weapp-EaseBook 易订包间

## 🎈项目简介
易订包间（EaseBook）是一款专为高端餐厅定制的智能包间预订小程序，致力于为商家和用户打造极致的订座体验。
本项目包含三端系统：
- 微信小程序用户端 miniprogram
- 后台管理端 vue3-management
- 服务器端 fastapi-backend

## 👈技术栈：
- 用户端：微信小程序
- 管理端：Vue3
- 服务端：FastAPI
- 数据库：MySQL、PostgreSQL
- 缓存：Redis

## 🌍项目部署
```bash
# 克隆项目
git clone https://gitee.com/yoke8/weapp-ease-book.git
# 进入项目根目录
cd weapp-ease-book
# 本地路径
E:\MyProjects\wechatAppProjects\weapp-ease-book
```


## 📈打包指令

## ⛺接口文档


## 📊版本更新

在对应的版本更新日志中编辑
**V1.0.20250801.1**
1. 初始化项目
