# weapp-EaseBook 易订包间-小程序端

# 1. 项目简介
微信小程序开发邀请函、订台小程序，专为酒店餐厅定制
- **项目名称**：易订包间EaseBook
- **定位**：高端餐厅包间可视化预订平台
- **技术栈**：微信小程序原生 + VantWeapp + Fastapi后端
- **特色**：3D包间展示、智能时段推荐、多角色协同管理

# 2. 界面设计规范

## 2.1. 视觉风格

- **主色调**：白色简洁
- **字体**：标题方正宋黑简体 / 正文思源柔黑体
- **设计原则**：高端餐饮质感 + 极简操作流


## 2.2. 核心页面框架

客户端

| 页面   | 核心组件            | 交互特色                                         |
| ---- | --------------- | -------------------------------------------- |
| 首页   | 客户预定包间首页        | 门店基本信息、位置、装修、特色菜品、营业时间、联系人、预定按钮进入包间选择        |
| 包间列表 | 卡片包间列表/平面图切换选项卡 | 剩余包间、卡片信息包含：包间环境简图、包间号、预定状态、用餐人数、包间经理等信息     |
| 预定表单 | 预定包间表单          | 填写预定人先生/女士、手机号、到店日期时间、用餐人数、忌口、备注信息、车牌号，生成邀请函 |
| 预订页  | 动态时间轴+人数适配算法    | 滑动选择时段实时计价                                   |
| 我的预定 | 预订记录列表          | 包含了预定包间的记录，支持取消预定、修改预定，点击后能够进入预定表单、查看邀请函     |
| 首页下角 | 地址卡片            | 点击跳出地图导航                                     |
| 邀请函  |                 | 右下角“我要预定”                                    |
|      |                 |                                              |
- 预定金、预点菜（备菜）、忌口
- 人数、大人小孩、 3D选位置 （靠窗）
- 客户信息
	- 预订人、电话、车牌号（一键停车券，免去扫码）、到店日期时间
- 短信发送
- 邀请函
- 停车信息
- 导航定位
- 开发票
- 了解“易订包间”

商家端
- 门店客户信息
- 订单状态
- 操作记录
- 统计


# 功能模块设计

## 1. 用户端功能

```mermaid
graph TD
    A[用户系统] --> B[智能预订]
    A --> C[沉浸式选房]
    A --> D[社交化服务]
    
    B --> B1[多条件筛选器]
    B --> B2[动态价格日历]
    B --> B3[宴席需求清单]
    
    C --> C1[3D图片实景看包间]
    C --> C2[设备控制模拟]
    C --> C3[餐桌3D布置图]
    
    D --> D1[邀请共订]
    D --> D2[菜品投票系统]
    D --> D3[位置共享]
```


# 项目结构


# 安装教程

1.  xxxx
2.  xxxx
3.  xxxx

#### 使用说明

1.  xxxx
2.  xxxx
3.  xxxx

#### 参与贡献

1.  Fork 本仓库
2.  新建 Feat_xxx 分支
3.  提交代码
4.  新建 Pull Request


#### 特技

1.  使用 Readme\_XXX.md 来支持不同的语言，例如 Readme\_en.md, Readme\_zh.md
2.  Gitee 官方博客 [blog.gitee.com](https://blog.gitee.com)
3.  你可以 [https://gitee.com/explore](https://gitee.com/explore) 这个地址来了解 Gitee 上的优秀开源项目
4.  [GVP](https://gitee.com/gvp) 全称是 Gitee 最有价值开源项目，是综合评定出的优秀开源项目
5.  Gitee 官方提供的使用手册 [https://gitee.com/help](https://gitee.com/help)
6.  Gitee 封面人物是一档用来展示 Gitee 会员风采的栏目 [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
