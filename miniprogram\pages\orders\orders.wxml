<!--pages/orders/orders.wxml-->
<view class="page-container">
  <!-- 标签页导航 -->
  <view class="tabs-container">
    <view class="tabs-wrapper">
      <view 
        class="tab-item {{currentTab === item.key ? 'active' : ''}}"
        wx:for="{{tabList}}" 
        wx:key="key"
        bindtap="onTabChange"
        data-tab="{{item.key}}">
        <text class="tab-text">{{item.label}}</text>
        <view class="tab-indicator" wx:if="{{currentTab === item.key}}"></view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <text class="loading-text">⏳ 加载中...</text>
  </view>

  <!-- 订单列表 -->
  <view class="orders-container" wx:else>
    <view wx:if="{{filteredOrders.length === 0}}" class="empty-container">
      <view class="empty-icon">📋</view>
      <view class="empty-text">暂无预订记录</view>
      <button class="btn-ghost empty-btn" bindtap="onGoBooking">去预订包间</button>
    </view>

    <view wx:else class="orders-list">
      <view 
        class="order-card {{item._id === highlightOrderId ? 'highlight' : ''}}"
        wx:for="{{filteredOrders}}" 
        wx:key="_id"
        bindtap="onViewOrderDetail"
        data-id="{{item._id}}">
        
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-title">{{item.roomInfo.name}}</text>
            <text class="order-type">{{item.roomInfo.type === 'private' ? '包间' : '大厅'}}</text>
          </view>
          <view class="order-status {{item.statusClass}}">
            {{item.statusText}}
          </view>
        </view>

        <!-- 预订信息 -->
        <view class="booking-info">
          <view class="info-row">
            <text class="info-label">用餐时间：</text>
            <text class="info-value">{{item.booking.date}} {{item.booking.timeStart}}-{{item.booking.timeEnd}}</text>
          </view>
          <view class="info-row">
            <text class="info-label">用餐人数：</text>
            <text class="info-value">{{item.booking.guestCount}}人</text>
          </view>
          <view class="info-row">
            <text class="info-label">联系人：</text>
            <text class="info-value">{{item.customer.name}} {{item.customer.phone}}</text>
          </view>
          <view class="info-row" wx:if="{{item.notes}}">
            <text class="info-label">备注：</text>
            <text class="info-value">{{item.notes}}</text>
          </view>
        </view>

        <!-- 费用信息 -->
        <view class="payment-info">
          <view class="payment-row">
            <text class="payment-label">定金：</text>
            <text class="payment-value">¥{{item.payment.deposit}}</text>
          </view>
          <view class="payment-row">
            <text class="payment-label">总费用：</text>
            <text class="payment-value total">¥{{item.payment.total}}</text>
          </view>
          <view class="payment-status">
            {{item.paymentStatusText}}
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="order-actions">
          <view class="action-buttons">
            <button 
              class="btn-ghost action-btn" 
              wx:if="{{item.status === 'pending' || item.status === 'confirmed'}}"
              bindtap="onCancelOrder"
              data-id="{{item._id}}">
              取消预订
            </button>
            <button 
              class="btn-secondary action-btn" 
              wx:if="{{item.status === 'completed' || item.status === 'cancelled'}}"
              bindtap="onBookAgain"
              data-id="{{item._id}}">
              再次预订
            </button>
            <button 
              class="btn-primary action-btn invitation-btn" 
              wx:if="{{item.status === 'confirmed' || item.status === 'completed'}}"
              bindtap="onOpenInvitation"
              data-id="{{item._id}}">
              邀请函
            </button>
            <button 
              class="btn-ghost action-btn" 
              bindtap="onContactService">
              联系客服
            </button>
          </view>
          <view class="order-time">
            <text class="time-text">下单时间：{{item.formattedCreateTime}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>