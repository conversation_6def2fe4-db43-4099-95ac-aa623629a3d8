/* pages/booking/booking.wxss */

/* 包间信息卡片 */
.room-info-card {
  margin-top: 0;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.room-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.room-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

.room-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.detail-item {
  display: flex;
  align-items: center;
}

.detail-label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
}

.detail-value {
  font-size: 28rpx;
  color: #333;
}

/* 表单样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.form-picker {
  height: 88rpx;
  line-height: 88rpx;
  padding: 0 24rpx;
  font-size: 32rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
  color: #333;
}

.form-picker:empty::before {
  content: attr(placeholder);
  color: #999;
}

/* 时间行布局 */
.time-row {
  display: flex;
  gap: 20rpx;
}

.time-item {
  flex: 1;
}

/* 人数计数器 */
.guest-counter {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 30rpx;
}

.counter-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  font-weight: bold;
  color: #ff6b35;
  border: 2rpx solid #ff6b35;
  border-radius: 50%;
  background-color: #fff;
}

.counter-btn.disabled {
  color: #ccc;
  border-color: #ccc;
}

.counter-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  min-width: 100rpx;
  text-align: center;
}

/* 费用信息 */
.cost-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.cost-label {
  font-size: 28rpx;
  color: #666;
}

.cost-value {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.cost-deposit {
  color: #ff6b35;
}

.cost-extra {
  color: #fa8c16;
}

.cost-divider {
  height: 1rpx;
  background-color: #e5e5e5;
  margin: 20rpx 0;
}

.cost-total {
  margin-bottom: 10rpx;
}

.cost-total .cost-label {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.cost-total .cost-value {
  font-size: 36rpx;
  color: #ff6b35;
}

.cost-note {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
  line-height: 1.5;
}

.cost-note view {
  margin-bottom: 4rpx;
}

.cost-note view:last-child {
  margin-bottom: 0;
}

/* 提交区域 */
.submit-section {
  padding: 30rpx 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn::after {
  border: none;
}

.submit-btn.disabled {
  background-color: #ccc !important;
  color: #fff;
}

.submit-tips {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}