/* 赴约人员列表页面样式 */
.guests-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #8B0000 0%, #CD5C5C 100%);
  padding: 40rpx 30rpx 120rpx;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  margin-top: 200rpx;
  color: #fff;
  font-size: 32rpx;
}

/* 赴约人员列表 */
.guests-list {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  min-height: 400rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 0;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 15rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* 人员网格 */
.guest-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25rpx;
}

/* 赴约人员项 */
.guest-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 15rpx;
  padding: 25rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.guest-item:active {
  transform: scale(0.98);
}

.guest-avatar {
  margin-right: 20rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  border: 3rpx solid #8B0000;
}

.guest-info {
  flex: 1;
}

.guest-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.confirm-time {
  font-size: 22rpx;
  color: #999;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-top: 2rpx solid rgba(139, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.back-btn {
  width: 100%;
  background: #8B0000;
  color: #fff;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}