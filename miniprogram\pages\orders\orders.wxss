/* pages/orders/orders.wxss */

/* 标签页导航 */
.tabs-container {
  background-color: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tabs-wrapper {
  display: flex;
  padding: 0 32rpx;
}

.tab-item {
  flex: 1;
  position: relative;
  text-align: center;
  padding: 24rpx 0;
  cursor: pointer;
}

.tab-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.tab-item.active .tab-text {
  color: var(--primary-gold);
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: var(--primary-gold);
  border-radius: 2rpx;
}

/* 加载状态 */
.loading-container {
  padding: 120rpx 0;
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-btn {
  margin: 0 auto;
}

/* 订单列表 */
.orders-container {
  padding: 20rpx;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

/* 订单卡片 */
.order-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
  padding: 30rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.order-card.highlight {
  border-color: var(--primary-gold);
  box-shadow: 0 4rpx 30rpx rgba(212, 175, 55, 0.2);
}

.order-card:active {
  transform: scale(0.98);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.order-type {
  font-size: 24rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
}

.status-confirmed {
  background-color: #f6ffed;
  color: #52c41a;
}

.status-completed {
  background-color: #f0f0f0;
  color: #666;
}

.status-cancelled {
  background-color: #fff2f0;
  color: #f5222d;
}

/* 预订信息 */
.booking-info {
  margin-bottom: 24rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  align-items: flex-start;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 26rpx;
  color: #666;
  min-width: 140rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 费用信息 */
.payment-info {
  background-color: #fafafa;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.payment-row:last-child {
  margin-bottom: 16rpx;
}

.payment-label {
  font-size: 26rpx;
  color: #666;
}

.payment-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.payment-value.total {
  font-size: 30rpx;
  color: var(--primary-gold);
  font-weight: bold;
}

.payment-status {
  text-align: center;
  font-size: 24rpx;
  color: #52c41a;
  padding: 8rpx;
  background-color: #f6ffed;
  border-radius: 8rpx;
}

/* 操作按钮 */
.order-actions {
  /* border-top: 1rpx solid #f0f0f0; */
  padding-top: 3rpx;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  border-radius: 8rpx;

  display: flex !important;
  align-items: center !important; /* 垂直居中 */
  justify-content: center !important; /* 水平居中 */
  text-align: center !important; /* 文本水平居中 */
}

.btn-primary {
  background: #8B0000;
  color: #fff;
}

.invitation-btn {
  background: linear-gradient(135deg, #8B0000 0%, #CD5C5C 100%);
  box-shadow: 0 4rpx 12rpx rgba(139, 0, 0, 0.3);
  font-weight: bold;
}

.order-time {
  text-align: center;
}

.time-text {
  font-size: 22rpx;
  color: #999;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .order-card {
    padding: 20rpx;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    width: 100%;
  }
}