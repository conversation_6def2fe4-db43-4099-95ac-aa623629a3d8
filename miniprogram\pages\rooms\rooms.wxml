<!--pages/rooms/rooms.wxml-->
<view class="page-container">
  <!-- 筛选栏 -->
  <view class="filter-bar">
    <view class="filter-item" bindtap="onShowFilter">
      <text class="filter-icon">🔍</text>
      <text class="filter-text">筛选</text>
    </view>
    
    <view class="filter-summary">
      <text class="summary-text" wx:if="{{filterParams.type !== 'all'}}">{{getTypeLabel(filterParams.type)}}</text>
      <text class="summary-text" wx:if="{{filterParams.capacity > 0}}">{{getCapacityLabel(filterParams.capacity)}}</text>
      <text class="summary-text" wx:if="{{filterParams.date}}">{{filterParams.date}}</text>
    </view>
    
    <view class="filter-count">
      <text class="count-text">共{{rooms.length}}间</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading && !refreshing}}">
    <text class="loading-text">⏳ 加载中...</text>
  </view>

  <!-- 包间列表 -->
  <view class="rooms-list" wx:else>
    <view wx:if="{{rooms.length === 0}}" class="empty">
      <view class="empty-icon">🏠</view>
      <view class="empty-text">暂无符合条件的包间</view>
      <button class="btn-ghost" bindtap="onResetFilter">重置筛选</button>
    </view>
    
    <view wx:else>
      <room-card 
        wx:for="{{rooms}}" 
        wx:key="_id"
        room="{{item}}"
        bind:tap="{{item.status === 'available' ? 'onViewRoom' : ''}}"
        bind:book="onBookRoom"
        data-id="{{item._id}}">
      </room-card>
    </view>
  </view>

  <!-- 筛选面板 -->
  <view class="filter-panel" wx:if="{{showFilter}}">
    <!-- <view class="overlay" bindtap="onHideFilter"></view> -->
    
    <view class="filter-content">
      <view class="filter-header">
        <text class="filter-title">筛选条件</text>
        <text class="filter-reset" bindtap="onResetFilter">重置</text>
      </view>
      
      <!-- 包间类型 -->
      <view class="filter-section">
        <view class="section-title">包间类型</view>
        <view class="option-grid">
          <view 
            class="option-item {{filterParams.type === item.value ? 'active' : ''}}"
            wx:for="{{typeOptions}}" 
            wx:key="value"
            bindtap="onSelectType"
            data-value="{{item.value}}">
            {{item.label}}
          </view>
        </view>
      </view>
      
      <!-- 用餐人数 -->
      <view class="filter-section">
        <view class="section-title">用餐人数</view>
        <view class="option-grid">
          <view 
            class="option-item {{filterParams.capacity === item.value ? 'active' : ''}}"
            wx:for="{{capacityOptions}}" 
            wx:key="value"
            bindtap="onSelectCapacity"
            data-value="{{item.value}}">
            {{item.label}}
          </view>
        </view>
      </view>
      
      <!-- 预订时间 -->
      <view class="filter-section">
        <view class="section-title">预订时间</view>
        
        <view class="time-picker-group">
          <view class="time-item">
            <text class="time-label">用餐日期</text>
            <picker mode="date" value="{{filterParams.date}}" bindchange="onDateChange">
              <view class="time-value">{{filterParams.date || '选择日期'}}</view>
            </picker>
          </view>
          
          <view class="time-item">
            <text class="time-label">开始时间</text>
            <picker mode="time" value="{{filterParams.timeStart}}" bindchange="onTimeStartChange">
              <view class="time-value">{{filterParams.timeStart || '选择时间'}}</view>
            </picker>
          </view>
          
          <view class="time-item">
            <text class="time-label">结束时间</text>
            <picker mode="time" value="{{filterParams.timeEnd}}" bindchange="onTimeEndChange">
              <view class="time-value">{{filterParams.timeEnd || '选择时间'}}</view>
            </picker>
          </view>
        </view>
      </view>
      
      <!-- 底部按钮 -->
      <view class="filter-footer">
        <button class="btn-secondary filter-cancel" bindtap="onHideFilter">取消</button>
        <button class="btn-primary filter-confirm" bindtap="onApplyFilter">确定</button>
      </view>
    </view>
  </view>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>