const { mockDataAPI } = require('../../utils/mockData')

Page({
  data: {
    invitation: null,
    loading: true,
    userInfo: null,
    hasConfirmed: false,
    isOwner: false, // 是否是邀请人本人
    guestCount: 0
  },

  onLoad(options) {
    const { id, shareCode } = options
    
    if (id) {
      // 通过ID直接访问（邀请人访问）
      this.loadInvitationById(id)
      this.setData({ isOwner: true })
    } else if (shareCode) {
      // 通过分享码访问（被邀请人访问）
      this.loadInvitationByShareCode(shareCode)
      this.setData({ isOwner: false })
    }

    // 获取用户信息
    this.getUserInfo()
  },

  /**
   * 通过ID加载邀请函
   */
  loadInvitationById(id) {
    const invitation = mockDataAPI.getInvitationById(id)
    if (invitation) {
      this.setData({
        invitation: invitation,
        loading: false,
        guestCount: invitation.guests.length
      })
    } else {
      wx.showToast({
        title: '邀请函不存在',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 2000)
    }
  },

  /**
   * 通过分享码加载邀请函
   */
  loadInvitationByShareCode(shareCode) {
    const invitation = mockDataAPI.getInvitationByShareCode(shareCode)
    if (invitation) {
      this.setData({
        invitation: invitation,
        loading: false,
        guestCount: invitation.guests.length
      })
      
      // 检查当前用户是否已确认赴约
      wx.getStorage({
        key: 'userInfo',
        success: (res) => {
          const userInfo = res.data
          const hasConfirmed = invitation.guests.some(guest => guest.openid === userInfo.openid)
          this.setData({ hasConfirmed })
        }
      })
    } else {
      wx.showToast({
        title: '邀请函不存在',
        icon: 'error'
      })
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo() {
    wx.getUserProfile({
      desc: '用于确认赴约',
      success: (res) => {
        const userInfo = {
          openid: 'temp_openid_' + Date.now(), // 模拟openid
          nickName: res.userInfo.nickName,
          avatarUrl: res.userInfo.avatarUrl
        }
        this.setData({ userInfo })
        wx.setStorage({
          key: 'userInfo',
          data: userInfo
        })
      }
    })
  },

  /**
   * 确认赴约
   */
  confirmAttendance() {
    const { invitation, userInfo } = this.data
    
    if (!userInfo) {
      wx.showToast({
        title: '请先授权获取用户信息',
        icon: 'none'
      })
      this.getUserInfo()
      return
    }

    const success = mockDataAPI.confirmAttendance(invitation._id, userInfo)
    
    if (success) {
      wx.showToast({
        title: '确认赴约成功',
        icon: 'success'
      })
      
      // 更新邀请函数据
      const updatedInvitation = mockDataAPI.getInvitationById(invitation._id)
      this.setData({
        invitation: updatedInvitation,
        hasConfirmed: true,
        guestCount: updatedInvitation.guests.length
      })
    } else {
      wx.showToast({
        title: '确认失败',
        icon: 'error'
      })
    }
  },

  /**
   * 查看赴约人员
   */
  viewGuests() {
    const { invitation } = this.data
    wx.navigateTo({
      url: `/pages/guests/guests?invitationId=${invitation._id}`
    })
  },

  /**
   * 导航到餐厅
   */
  navigateToRestaurant() {
    const { invitation } = this.data
    const { restaurant } = invitation
    
    wx.openLocation({
      latitude: 23.1291, // 广州坐标示例
      longitude: 113.2644,
      name: restaurant.name,
      address: restaurant.address
    })
  },

  /**
   * 联系餐厅
   */
  callRestaurant() {
    const { invitation } = this.data
    wx.makePhoneCall({
      phoneNumber: invitation.restaurant.phone
    })
  },

  /**
   * 保存名片
   */
  saveContact() {
    const { invitation } = this.data
    wx.addPhoneContact({
      firstName: invitation.restaurant.managerName,
      mobilePhoneNumber: invitation.restaurant.phone,
      organization: invitation.restaurant.name
    })
  },

  /**
   * 导航回门店
   */
  navigateToStore() {
    wx.redirectTo({
      url: '/pages/index/index'
    })
  },

  /**
   * 确认赴约（按钮点击）
   */
  confirmReservation() {
    this.confirmAttendance()
  },

  /**
   * 分享给朋友
   */
  onShareAppMessage() {
    const { invitation } = this.data
    return {
      title: `${invitation.restaurant.name} 邀请函`,
      path: `/pages/invitation/invitation?shareCode=${invitation.shareCode}`,
      imageUrl: '/assets/images/share-invitation.jpg'
    }
  }
})