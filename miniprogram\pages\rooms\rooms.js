// pages/rooms/rooms.js
const app = getApp()
const { mockDataAPI } = require('../../utils/mockData')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    rooms: [],
    allRooms: [],
    loading: true,
    filterParams: {
      type: 'all', // all, private, hall
      capacity: 0, // 0表示不限制人数
      date: '', // 预订日期
      timeStart: '', // 开始时间
      timeEnd: '' // 结束时间
    },
    capacityOptions: [
      { value: 0, label: '不限人数' },
      { value: 4, label: '4人以下' },
      { value: 8, label: '4-8人' },
      { value: 12, label: '8-12人' },
      { value: 20, label: '12-20人' },
      { value: 999, label: '20人以上' }
    ],
    typeOptions: [
      { value: 'all', label: '全部' },
      { value: 'private', label: '包间' },
      { value: 'hall', label: '大厅' }
    ],
    showFilter: false,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 加载全局数据
    const allRooms = mockDataAPI.getRooms()
    this.setData({ allRooms })
    
    // 如果有传入的筛选参数，更新筛选条件
    if (options.capacity) {
      this.setData({
        'filterParams.capacity': parseInt(options.capacity)
      })
    }
    
    this.loadRooms()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 每次显示时刷新数据，因为房间状态可能发生变化
    this.loadRooms()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.setData({ refreshing: true })
    this.loadRooms()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    // 如果需要分页加载，在这里实现
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '乐聚轩饭店 - 包间预订',
      path: '/pages/rooms/rooms'
    }
  },

  /**
   * 加载包间数据
   */
  loadRooms() {
    console.log('loadRooms called', this.data.filterParams)
    
    // 直接同步筛选，不使用异步
    const filteredRooms = this.filterRooms(this.data.allRooms)
    
    this.setData({
      rooms: filteredRooms,
      loading: false
    })
    
    console.log('filtered rooms:', filteredRooms.length)
  },

  /**
   * 筛选包间
   */
  filterRooms(rooms) {
    const { type, capacity } = this.data.filterParams
    
    return rooms.filter(room => {
      // 按类型筛选
      if (type !== 'all' && room.type !== type) {
        return false
      }
      
      // 按人数筛选
      if (capacity > 0) {
        if (capacity === 4 && room.capacity.max > 4) return false
        if (capacity === 8 && (room.capacity.max < 4 || room.capacity.max > 8)) return false
        if (capacity === 12 && (room.capacity.max < 8 || room.capacity.max > 12)) return false
        if (capacity === 20 && (room.capacity.max < 12 || room.capacity.max > 20)) return false
        if (capacity === 999 && room.capacity.max < 20) return false
      }
      
      return true
    })
  },

  /**
   * 显示筛选面板
   */
  onShowFilter() {
    console.log('显示筛选面板')
    this.setData({ showFilter: true })
  },

  /**
   * 隐藏筛选面板
   */
  onHideFilter() {
    this.setData({ showFilter: false })
  },

  /**
   * 选择包间类型
   */
  onSelectType(e) {
    const type = e.currentTarget.dataset.value
    console.log('选择包间类型:', type)
    this.setData({
      'filterParams.type': type
    })
  },

  /**
   * 选择人数
   */
  onSelectCapacity(e) {
    const capacity = e.currentTarget.dataset.value
    console.log('选择人数:', capacity)
    this.setData({
      'filterParams.capacity': parseInt(capacity)
    })
  },

  /**
   * 选择日期
   */
  onDateChange(e) {
    this.setData({
      'filterParams.date': e.detail.value
    })
  },

  /**
   * 选择开始时间
   */
  onTimeStartChange(e) {
    this.setData({
      'filterParams.timeStart': e.detail.value
    })
  },

  /**
   * 选择结束时间
   */
  onTimeEndChange(e) {
    this.setData({
      'filterParams.timeEnd': e.detail.value
    })
  },

  /**
   * 应用筛选
   */
  onApplyFilter() {
    console.log('应用筛选:', this.data.filterParams)
    this.setData({ showFilter: false })
    this.loadRooms()
  },

  /**
   * 重置筛选
   */
  onResetFilter() {
    this.setData({
      filterParams: {
        type: 'all',
        capacity: 0,
        date: '',
        timeStart: '',
        timeEnd: ''
      }
    })
    this.loadRooms()
  },

  /**
   * 查看包间详情
   */
  onViewRoom(e) {
    const roomId = e.detail.roomId
    wx.navigateTo({
      url: `/pages/booking/booking?roomId=${roomId}`
    })
  },

  /**
   * 立即预订
   */
  onBookRoom(e) {
    const roomId = e.detail.roomId
    
    // 检查包间是否可用
    const room = this.data.rooms.find(r => r._id === roomId)
    if (room && room.status !== 'available') {
      wx.showToast({
        title: '该包间暂不可用',
        icon: 'none'
      })
      return
    }

    // 跳转到预订页面
    wx.navigateTo({
      url: `/pages/booking/booking?roomId=${roomId}`
    })
  },

  /**
   * 获取容量标签
   */
  getCapacityLabel(capacity) {
    const option = this.data.capacityOptions.find(opt => opt.value === capacity)
    return option ? option.label : '不限人数'
  },

  /**
   * 获取类型标签
   */
  getTypeLabel(type) {
    const option = this.data.typeOptions.find(opt => opt.value === type)
    return option ? option.label : '全部'
  },

  /**
   * 获取状态文本
   */
  getStatusText(status) {
    const statusMap = {
      'available': '可预订',
      'occupied': '已预订',
      'maintenance': '维护中'
    }
    return statusMap[status] || '未知状态'
  },

  /**
   * 获取状态样式类
   */
  getStatusClass(status) {
    return `status-${status}`
  },

  /**
   * 格式化价格
   */
  formatPrice(room) {
    if (room.price && room.price.base) {
      let priceText = `¥${room.price.base}`
      if (room.price.peak && room.price.peak !== room.price.base) {
        priceText += ` - ¥${room.price.peak}`
      }
      return priceText
    }
    return '价格面议'
  }
})