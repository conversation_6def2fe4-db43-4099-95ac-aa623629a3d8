# EaseBook 易订包间 - 开发方案

## 项目定位
- **目标用户**：中型粤菜酒楼（20个包间 + 100个大厅卡座）
- **产品路线**：实用工具路线，专注解决实际预订问题
- **开发目标**：快速上线MVP，后续迭代优化


## 技术架构

### 主要技术栈
```
├── 前端：原生微信小程序 + WeUI
├── 后端：微信云开发（主要）+ FastAPI（备选）
├── 数据库：微信云开发数据库
├── 存储：微信云开发存储
└── 支付：微信支付
```

### 技术选型说明
- **原生微信小程序**：性能最优，与微信生态完美融合，开发工具支持完善
- **WeUI**：微信官方UI组件库，设计规范统一
- **微信云开发**：免服务器运维，天然支持微信生态，90%功能可满足
- **FastAPI备选**：处理复杂业务逻辑、第三方集成、高并发场景

## 功能模块设计

### 用户端功能
1. **首页**：酒楼基本信息 + 特色菜品 + 一键预订
2. **包间选择**：按人数筛选 + 实时状态 + 包间费用
3. **预订表单**：联系人 + 用餐时间 + 人数 + 备注 + 定金
4. **订单管理**：查看/取消/修改预订 + 邀请函入口
5. **邀请函功能**：精美中式邀请函 + 确认赴约 + 赴约人员管理

### 商家端功能
1. **今日订单**：实时订单列表 + 快速确认
2. **包间管理**：状态切换 + 价格设置 + 维护标记
3. **客户档案**：常客信息 + 喜好记录
4. **营收统计**：日/月报表 + 包间利用率

## 数据库设计

### rooms集合（包间信息）
```javascript
{
  _id: "room_001",
  name: "雅韵1号包间",
  type: "private", // private包间/hall大厅
  capacity: {min: 8, max: 12},
  price: {base: 300, peak: 450}, // 基础价/高峰价
  status: "available", // available/occupied/maintenance
  images: ["cloud://xxx"],
  features: ["独立卫生间", "43寸电视"],
  floor: 2,
  area: 25, // 平方米
  createTime: Date,
  updateTime: Date
}
```

### orders集合（订单信息）
```javascript
{
  _id: "order_001", 
  roomId: "room_001",
  openid: "user_openid", // 微信用户标识
  customer: {
    name: "张先生",
    phone: "13800138000"
  },
  booking: {
    date: "2025-08-10",
    timeStart: "18:00",
    timeEnd: "21:00",
    guestCount: 10
  },
  payment: {
    deposit: 200,
    total: 900,
    method: "wechat", // wechat/cash
    status: "paid" // pending/paid/refunded
  },
  status: "confirmed", // pending/confirmed/cancelled/completed
  notes: "不要花生，加个生日蛋糕",
  createTime: Date,
  updateTime: Date
}
```

### customers集合（客户信息）
```javascript
{
  openid: "wx_openid",
  name: "张先生", 
  phone: "13800138000",
  visitCount: 5, // 到店次数
  lastVisit: "2025-07-20",
  preferences: "不吃辣", // 偏好记录
  vipLevel: "gold" // 会员等级
}
```

### invitations集合（邀请函信息）
```javascript
{
  _id: "invitation_001",
  orderId: "order_001", // 关联订单ID
  shareCode: "ABC12345", // 分享码
  guests: [
    {
      openid: "guest_openid",
      nickName: "张三",
      avatarUrl: "https://...",
      confirmTime: Date
    }
  ],
  createTime: Date,
  updateTime: Date
}
```

### restaurant_info集合（餐厅基本信息）
```javascript
{
  name: "乐聚轩饭店",
  slogan: "臻敬的盛宴，您好。您的到来，是我们最大的荣幸",
  address: "广东省广州市番禺区新港东路口大道口东街20号",
  phone: "020-12345678",
  managerName: "陈雅坚",
  managerTitle: "专属经理",
  environmentImages: ["云存储路径1", "云存储路径2"],
  dishImages: ["云存储路径3", "云存储路径4"]
}
```

## 项目目录结构

```
weapp-ease-book/
├── miniprogram/              # 小程序前端
│   ├── pages/               # 页面
│   │   ├── index/          # 首页（门店信息）
│   │   ├── rooms/          # 包间列表
│   │   ├── booking/        # 预订表单
│   │   ├── orders/         # 我的预订
│   │   ├── invitation/     # 邀请函页面
│   │   ├── guests/         # 赴约人员列表
│   │   └── admin/          # 商家管理端
│   ├── components/         # 公共组件
│   │   ├── room-card/      # 包间卡片
│   │   ├── time-picker/    # 时间选择器
│   │   └── order-item/     # 订单项
│   ├── utils/             # 工具函数
│   │   ├── request.js     # 云函数调用封装
│   │   ├── date.js        # 日期处理
│   │   └── validator.js   # 表单验证
│   ├── style/             # 全局样式
│   ├── app.js             # 小程序入口
│   ├── app.json           # 小程序配置
│   └── app.wxss           # 全局样式
├── cloudfunctions/         # 云函数
│   ├── auth/              # 用户认证
│   ├── room/              # 包间管理
│   │   ├── getRooms/     # 获取包间列表
│   │   ├── getRoomDetail/ # 包间详情
│   │   └── updateRoom/   # 更新包间状态
│   ├── booking/          # 预订相关  
│   │   ├── createOrder/  # 创建预订
│   │   ├── getOrders/    # 获取订单列表
│   │   ├── updateOrder/  # 更新订单状态
│   │   └── cancelOrder/  # 取消预订
│   └── admin/            # 管理功能
│       ├── getDashboard/ # 获取仪表盘数据
│       └── getStats/     # 营收统计
├── fastapi-backup/        # FastAPI备选方案
│   ├── main.py
│   ├── models/
│   ├── api/
│   └── requirements.txt
├── docs/                  # 文档
├── database/              # 数据库设计文档
├── README.md              # 项目说明
└── CLAUDE.md              # 开发方案（本文件）
```

## 云开发 vs FastAPI 功能分配

### 云开发负责（优先）
- ✅ 用户认证（微信登录）
- ✅ CRUD操作（包间、订单管理）
- ✅ 文件上传（包间图片）
- ✅ 定时任务（订单状态清理）
- ✅ 微信支付集成

### FastAPI备选场景
- 🔄 复杂业务逻辑（智能推荐算法）
- 🔄 第三方集成（短信、支付宝）
- 🔄 高并发场景（大促预订）
- 🔄 数据分析统计

## 开发时间规划（MVP版本）

### 第1周：基础架构
- [x] 项目结构搭建
- [x] 基础页面框架
- [x] 包间数据录入
- [ ] 云开发环境配置

### 第2周：预订流程
- [x] 包间列表展示
- [x] 预订表单开发
- [x] 订单创建流程
- [ ] 支付集成

### 第3周：邀请函功能
- [x] 邀请函页面设计与开发
- [x] 确认赴约功能
- [x] 赴约人员管理
- [x] 订单邀请函入口

### 第4周：管理功能
- [ ] 商家端订单管理
- [ ] 包间状态管理  
- [ ] 客户信息管理
- [ ] 基础统计功能

### 第5周：测试上线
- [ ] 功能测试
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 线上发布

## 邀请函功能详细说明

### 功能概述
邀请函功能是本项目的核心特色功能，采用中式设计风格，为用户提供精美的聚餐邀请体验。

### 核心功能流程
1. **邀请函生成**：用户预订成功后，系统弹窗询问是否生成邀请函
2. **邀请函展示**：显示预订详情、餐厅信息、经理联系方式、门店环境等
3. **分享传播**：用户可将邀请函分享给微信好友
4. **确认赴约**：被邀请人点击确认赴约，记录用户信息
5. **人员管理**：邀请人可查看确认赴约的人员列表

### 设计特色
- **中式风格**：红色渐变背景，金色元素点缀
- **半透明卡片**：重要信息以半透明卡片形式展示
- **丰富内容**：包含预订信息、餐厅介绍、环境图片、特色菜品
- **交互友好**：支持导航、通话、保存联系人等便民功能

### 技术实现
- **数据存储**：本地存储 + 全局数据管理
- **分享机制**：基于分享码的访问控制
- **用户识别**：微信用户信息获取与绑定
- **状态管理**：确认状态实时更新

## 后续迭代功能

### V2.0 功能增强
- 📱 邀请函模板多样化
- 🔔 微信消息通知
- 💰 会员积分系统
- 📊 高级数据分析

### V3.0 生态扩展
- 🍽️ 菜品预订功能
- 🚗 停车位管理
- 🎉 活动营销工具
- 🔗 第三方平台对接

## 注意事项

1. **数据安全**：敏感信息加密存储，权限控制严格
2. **性能优化**：分页加载，图片懒加载，缓存策略
3. **用户体验**：操作简单直观，错误提示友好
4. **商业逻辑**：包间冲突检测，超时订单处理
5. **扩展性**：模块化设计，便于后续功能添加

## 技术风险与应对

| 风险点 | 应对方案 |
|--------|----------|
| 云开发并发限制 | FastAPI分流高并发请求 |
| 数据库查询慢 | 索引优化 + 缓存策略 |
| 微信审核不过 | 功能简化 + 合规调整 |
| 用户体验差 | 多轮用户测试 + 快速迭代 |

---

**最后更新**：2025-08-04
**文档版本**：v1.1
**状态**：邀请函功能已完成，基础框架开发中