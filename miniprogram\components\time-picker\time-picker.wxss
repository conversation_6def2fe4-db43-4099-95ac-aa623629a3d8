/* components/time-picker/time-picker.wxss */
.time-picker {
  width: 100%;
}

.time-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.time-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.time-label {
  font-size: 24rpx;
  color: #666;
  font-weight: bold;
}

.time-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  background-color: #fff;
  transition: border-color 0.3s ease;
}

.time-selector:not(.disabled):active {
  border-color: #ff6b35;
}

.time-selector.disabled {
  background-color: #f5f5f5;
  color: #999;
}

.time-text {
  font-size: 28rpx;
  color: #333;
}

.time-text.placeholder {
  color: #999;
}

.time-divider {
  font-size: 24rpx;
  color: #666;
  margin-top: 36rpx;
  text-align: center;
  min-width: 40rpx;
}