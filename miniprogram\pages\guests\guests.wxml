<!-- 赴约人员列表页面 -->
<view class="guests-container">
  <!-- 头部 -->
  <view class="header">
    <view class="title">确认赴约名单</view>
    <view class="subtitle">共{{guests.length}}位朋友确认参加</view>
  </view>

  <!-- 加载中 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 赴约人员列表 -->
  <view wx:else class="guests-list">
    <view wx:if="{{guests.length === 0}}" class="empty-state">
      <view class="empty-icon">👥</view>
      <view class="empty-text">还没有朋友确认赴约</view>
      <view class="empty-desc">分享邀请函让更多朋友参与吧</view>
    </view>

    <view wx:else class="guest-grid">
      <view 
        wx:for="{{guests}}" 
        wx:key="openid"
        class="guest-item">
        <view class="guest-avatar">
          <image src="{{item.avatarUrl}}" class="avatar" mode="aspectFill"></image>
        </view>
        <view class="guest-info">
          <view class="guest-name">{{item.nickName}}</view>
          <view class="confirm-time">{{item.confirmTime}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="back-btn" bindtap="goBack">返回邀请函</button>
  </view>
</view>