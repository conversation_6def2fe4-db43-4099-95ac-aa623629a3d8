/* components/room-card/room-card.wxss */
.room-card {
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.room-card:active {
  transform: scale(0.98);
}

.room-card.card-disabled {
  opacity: 0.8;
}

.room-card.card-disabled:active {
  transform: none;
}

/* 图片容器 */
.room-image-container {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.room-image {
  width: 100%;
  height: 100%;
}

.room-status {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.status-available {
  background-color: rgba(82, 196, 26, 0.9);
  color: #fff;
}

.status-occupied {
  background-color: rgba(245, 34, 45, 0.9);
  color: #fff;
}

.status-maintenance {
  background-color: rgba(250, 173, 20, 0.9);
  color: #fff;
}

.image-count {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.6);
}

.count-icon {
  font-size: 20rpx;
  color: #fff;
}

.detail-icon {
  font-size: 24rpx;
  margin-right: 8rpx;
}

.count-text {
  font-size: 20rpx;
  color: #fff;
}

/* 内容区域 */
.room-content {
  padding: 30rpx;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.room-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.room-price {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b35;
}

/* 详情信息 */
.room-details {
  margin-bottom: 30rpx;
}

.detail-row {
  display: flex;
  gap: 30rpx;
  margin-bottom: 16rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.detail-text {
  font-size: 24rpx;
  color: #666;
}

.room-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.feature-tag {
  font-size: 20rpx;
  color: #666;
  background-color: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

/* 操作按钮 */
.room-actions {
  padding: 20rpx;
  background-color: #f8f9fa;
}

.room-actions.disabled-section {
  background-color: #f0f0f0;
}

.book-btn {
  width: 100% !important;
  height: 40rpx !important;
  line-height: 80rpx !important;
  font-size: 28rpx !important;
  border-radius: 8rpx !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-align: center !important;
  font-weight: 500 !important;
  box-shadow: none !important;
}

.book-btn.disabled {
  background-color: #e5e5e5 !important;
  color: #999 !important;
  border: 1rpx solid #d9d9d9 !important;
  opacity: 0.7;
  cursor: not-allowed;
}

.book-btn.disabled:active {
  transform: none !important;
  background-color: #e5e5e5 !important;
}