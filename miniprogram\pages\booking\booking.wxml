<!--pages/booking/booking.wxml-->
<view class="page-container">
  <!-- 包间信息 -->
  <view class="card room-info-card">
    <view class="card-content">
      <view class="room-header">
        <view class="room-name">{{roomInfo.name}}</view>
        <view class="room-price">{{roomInfo.price.base}}元起</view>
      </view>
      
      <view class="room-details">
        <view class="detail-item">
          <text class="detail-label">容纳人数：</text>
          <text class="detail-value">{{roomInfo.capacity.min}}-{{roomInfo.capacity.max}}人</text>
        </view>
        <view class="detail-item">
          <text class="detail-label">包间面积：</text>
          <text class="detail-value">{{roomInfo.area}}平方米</text>
        </view>
        <view class="detail-item" wx:if="{{roomInfo.features}}">
          <text class="detail-label">包间设施：</text>
          <text class="detail-value">{{roomInfo.features.join('、')}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 预订表单 -->
  <form bindsubmit="onSubmitBooking">
    <!-- 客户信息 -->
    <view class="card">
      <view class="card-content">
        <view class="section-title">客户信息</view>
        
        <view class="form-item">
          <view class="form-label">联系人姓名 *</view>
          <input 
            class="form-input" 
            placeholder="请输入联系人姓名" 
            value="{{bookingForm.customerName}}"
            data-field="customerName"
            bindinput="onInputChange"
            maxlength="20" />
        </view>
        
        <view class="form-item">
          <view class="form-label">联系电话 *</view>
          <input 
            class="form-input" 
            placeholder="请输入手机号码" 
            value="{{bookingForm.customerPhone}}"
            data-field="customerPhone"
            bindinput="onInputChange"
            type="number"
            maxlength="11" />
        </view>
      </view>
    </view>

    <!-- 预订信息 -->
    <view class="card">
      <view class="card-content">
        <view class="section-title">预订信息</view>
        
        <view class="form-item">
          <view class="form-label">用餐日期 *</view>
          <picker 
            mode="date" 
            value="{{bookingForm.bookingDate}}" 
            start="{{minDate}}"
            end="{{maxDate}}"
            bindchange="onDateChange">
            <view class="form-picker">
              {{bookingForm.bookingDate || '请选择用餐日期'}}
            </view>
          </picker>
        </view>
        
        <view class="time-row">
          <view class="time-item">
            <view class="form-label">开始时间 *</view>
            <picker 
              range="{{timeSlots}}" 
              value="{{getTimeIndex(bookingForm.timeStart)}}"
              data-field="timeStart"
              bindchange="onTimeChange">
              <view class="form-picker">
                {{bookingForm.timeStart || '开始时间'}}
              </view>
            </picker>
          </view>
          
          <view class="time-item">
            <view class="form-label">结束时间 *</view>
            <picker 
              range="{{timeSlots}}" 
              value="{{getTimeIndex(bookingForm.timeEnd)}}"
              data-field="timeEnd"
              bindchange="onTimeChange">
              <view class="form-picker">
                {{bookingForm.timeEnd || '结束时间'}}
              </view>
            </picker>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">用餐人数 *</view>
          <view class="guest-counter">
            <view 
              class="counter-btn {{bookingForm.guestCount <= 1 ? 'disabled' : ''}}"
              data-change="decrease"
              bindtap="onGuestCountChange">
              -
            </view>
            <view class="counter-value">{{bookingForm.guestCount}}人</view>
            <view 
              class="counter-btn"
              data-change="increase"
              bindtap="onGuestCountChange">
              +
            </view>
          </view>
        </view>
        
        <view class="form-item">
          <view class="form-label">备注信息</view>
          <textarea 
            class="form-textarea" 
            placeholder="请输入特殊需求或备注信息" 
            value="{{bookingForm.notes}}"
            data-field="notes"
            bindinput="onInputChange"
            maxlength="200"
            show-confirm-bar="{{false}}" />
        </view>
      </view>
    </view>

    <!-- 费用信息 -->
    <view class="card">
      <view class="card-content">
        <view class="section-title">费用信息</view>
        
        <view class="cost-item">
          <text class="cost-label">包间费用</text>
          <text class="cost-value">¥{{costInfo.roomFee}}</text>
        </view>
        
        <view class="cost-item" wx:if="{{costInfo.durationFee > 0}}">
          <text class="cost-label">超时费用</text>
          <text class="cost-value cost-extra">¥{{costInfo.durationFee}}</text>
        </view>
        
        <view class="cost-item">
          <text class="cost-label">需付定金</text>
          <text class="cost-value cost-deposit">¥{{costInfo.deposit}}</text>
        </view>
        
        <view class="cost-divider"></view>
        
        <view class="cost-item cost-total">
          <text class="cost-label">预计总费用</text>
          <text class="cost-value">¥{{costInfo.total}}</text>
        </view>
        
        <view class="cost-note">
          <view>* 定金将在到店时抵扣总费用</view>
          <view wx:if="{{costInfo.durationFee > 0}}">* 超过3小时用餐每小时加收50元</view>
          <view wx:if="{{roomInfo.type === 'hall'}}">* 大厅用餐无需定金</view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="btn-primary submit-btn {{!formValid || submitting ? 'disabled' : ''}}" 
        form-type="submit"
        disabled="{{!formValid || submitting}}">
        {{submitting ? '提交中...' : '确认预订'}}
      </button>
      
      <view class="submit-tips">
        点击确认预订即表示您同意我们的服务条款
      </view>
    </view>
  </form>

  <!-- 底部安全区域 -->
  <view class="safe-area-bottom"></view>
</view>

