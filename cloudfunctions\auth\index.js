// 云函数入口文件
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action } = event

  try {
    switch (action) {
      case 'login':
        return await handleLogin(event, wxContext)
      case 'getUserInfo':
        return await handleGetUserInfo(event, wxContext)
      case 'updateUserInfo':
        return await handleUpdateUserInfo(event, wxContext)
      default:
        return {
          code: -1,
          message: '无效的操作类型'
        }
    }
  } catch (error) {
    console.error('用户认证错误:', error)
    return {
      code: -1,
      message: '服务器内部错误',
      error: error.message
    }
  }
}

/**
 * 处理用户登录
 */
async function handleLogin(event, wxContext) {
  const { openid, unionid } = wxContext
  
  try {
    // 检查用户是否已存在
    const existingUser = await db.collection('customers').where({
      openid: openid
    }).get()

    let userData
    if (existingUser.data.length === 0) {
      // 新用户，创建用户记录
      const createResult = await db.collection('customers').add({
        data: {
          openid: openid,
          unionid: unionid,
          name: '',
          phone: '',
          visitCount: 0,
          lastVisit: null,
          preferences: '',
          vipLevel: 'regular',
          createTime: new Date(),
          updateTime: new Date()
        }
      })
      
      userData = {
        _id: createResult._id,
        openid: openid,
        isNewUser: true
      }
    } else {
      // 老用户，更新最后登录时间
      const user = existingUser.data[0]
      await db.collection('customers').doc(user._id).update({
        data: {
          lastLogin: new Date(),
          updateTime: new Date()
        }
      })
      
      userData = {
        ...user,
        isNewUser: false
      }
    }

    return {
      code: 0,
      message: '登录成功',
      data: userData
    }
  } catch (error) {
    console.error('登录处理错误:', error)
    throw error
  }
}

/**
 * 获取用户信息
 */
async function handleGetUserInfo(event, wxContext) {
  const { openid } = wxContext
  
  try {
    const result = await db.collection('customers').where({
      openid: openid
    }).get()

    if (result.data.length === 0) {
      return {
        code: -1,
        message: '用户不存在'
      }
    }

    return {
      code: 0,
      message: '获取成功',
      data: result.data[0]
    }
  } catch (error) {
    console.error('获取用户信息错误:', error)
    throw error
  }
}

/**
 * 更新用户信息
 */
async function handleUpdateUserInfo(event, wxContext) {
  const { openid } = wxContext
  const { userInfo } = event
  
  try {
    const updateData = {
      ...userInfo,
      updateTime: new Date()
    }
    
    // 移除不允许更新的字段
    delete updateData.openid
    delete updateData._id
    delete updateData.createTime
    
    const result = await db.collection('customers').where({
      openid: openid
    }).update({
      data: updateData
    })

    if (result.stats.updated === 0) {
      return {
        code: -1,
        message: '用户不存在或更新失败'
      }
    }

    return {
      code: 0,
      message: '更新成功',
      data: result
    }
  } catch (error) {
    console.error('更新用户信息错误:', error)
    throw error
  }
}