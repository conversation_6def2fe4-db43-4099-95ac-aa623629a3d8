/* 邀请函页面样式 */
.invitation-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #8B0000 0%, #CD5C5C 100%);
}

/* 背景图 */
.background-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.bg-img {
  width: 100%;
  height: 100%;
  opacity: 0.3;
}

/* 加载状态 */
.loading-container {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.loading-text {
  color: #fff;
  font-size: 32rpx;
  text-align: center;
}

/* 邀请函内容 */
.invitation-content {
  position: relative;
  z-index: 5;
  padding: 40rpx 30rpx 120rpx;
}

/* 卡片通用样式 */
.card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

/* 预订成功卡片 */
.booking-success-card {
  background: rgba(255, 248, 220, 0.95);
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B0000;
  text-align: center;
  margin-bottom: 20rpx;
}

.restaurant-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #8B0000;
  text-align: center;
  margin-bottom: 30rpx;
}

.booking-details {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 15rpx;
  padding: 25rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  font-size: 28rpx;
}

.value {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

/* 确认人数 */
.confirmed-count {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 25rpx;
  padding: 20rpx;
  background: rgba(139, 0, 0, 0.1);
  border-radius: 15rpx;
}

.count-icon {
  font-size: 32rpx;
  margin-right: 10rpx;
}

.count-text {
  color: #8B0000;
  font-size: 28rpx;
  margin-right: 20rpx;
}

.view-list {
  color: #007AFF;
  font-size: 26rpx;
  text-decoration: underline;
}

/* 经理联系卡片 */
.manager-card {
  display: flex;
  align-items: center;
  background: rgba(240, 248, 255, 0.95);
}

.manager-avatar {
  margin-right: 20rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 4rpx solid #8B0000;
}

.manager-info {
  flex: 1;
}

.manager-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 5rpx;
}

.manager-title {
  font-size: 24rpx;
  color: #8B0000;
  margin-bottom: 10rpx;
}

.manager-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
}

.manager-actions {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.action-btn {
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: none;
}

.contact-btn {
  background: #8B0000;
  color: #fff;
}

.save-btn {
  background: #f0f0f0;
  color: #333;
}

/* 导航卡片 */
.navigation-card {
  display: flex;
  align-items: center;
  background: rgba(255, 248, 220, 0.95);
  cursor: pointer;
}

.nav-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.nav-info {
  flex: 1;
}

.nav-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.nav-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}

.nav-action {
  background: #8B0000;
  color: #fff;
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
}

/* 章节标题 */
.section-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  margin: 40rpx 0 30rpx;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

/* 环境图片画廊 */
.environment-gallery {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.environment-img {
  width: 220rpx;
  height: 160rpx;
  border-radius: 15rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 菜品图片画廊 */
.dish-gallery {
  display: flex;
  justify-content: space-around;
  margin-bottom: 40rpx;
}

.dish-img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

/* 底部操作区 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-top: 2rpx solid rgba(139, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
  z-index: 10;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-buttons .action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 25rpx;
  font-size: 30rpx;
  font-weight: bold;
  border: none;
}

.btn-ghost {
  background: #f0f0f0;
  color: #333;
}

.btn-primary {
  background: #8B0000;
  color: #fff;
}

.btn-success {
  background: #28a745;
  color: #fff;
}

.disabled {
  opacity: 0.6;
}